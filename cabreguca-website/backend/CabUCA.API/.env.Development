# .env.Development

# Set environment to Development
ASPNETCORE_ENVIRONMENT=Development

# Enable HTTPS in development mode
DEV_HTTPS=true

# JWT secret for development
JWT_SECRET=dev-jwt-secret-for-local-testing-only

# Admin user credentials for development
ADMIN_USERNAME=DevAdmin
ADMIN_PASSWORD=DevPassword123!
ADMIN_EMAIL=<EMAIL>

# Data Protection Keys - These will be auto-detected in development mode
# You can leave these blank as we'll use the ASP.NET Core dev certificate
DATA_PROTECTION_CERT_PASSWORD=
DATA_PROTECTION_CERT_PATH=placeholder

# REACT_APP environment variables (for admin-frontend)
REACT_APP_ORGANISATION_NAME="Caboolture UCA"
REACT_APP_ORGANISATION_FULL_NAME="Caboolture Uniting Church Australia"
REACT_APP_CONTACT_EMAIL="<EMAIL>"
REACT_APP_CONTACT_PHONE="(07) 5499 3411"
REACT_APP_CONTACT_ADDRESS="Caboolture Uniting Church, Corner of King Street and Smiths Road."
REACT_APP_API_DOMAIN_NAME=localhost:8080
REACT_APP_DOMAIN_NAME=localhost:3000
REACT_APP_ADMIN_DOMAIN_NAME=localhost:3000
