#!/bin/bash
# Simple environment switcher

if [ "$1" == "dev" ]; then
    echo "Switching to development environment"
    cp .env.Development .env
    cp .env CabUCA.API/.env 2>/dev/null || echo "Note: Failed to copy to API directory"
elif [ "$1" == "prod" ]; then
    echo "Switching to production environment"
    cp .env.Production .env
    cp .env CabUCA.API/.env 2>/dev/null || echo "Note: Failed to copy to API directory"
else
    echo "Usage: $0 [dev|prod]"
fi