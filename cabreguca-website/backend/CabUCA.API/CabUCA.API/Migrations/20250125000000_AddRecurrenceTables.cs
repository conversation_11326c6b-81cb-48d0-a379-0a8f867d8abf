using Microsoft.EntityFrameworkCore.Migrations;

public partial class AddRecurrenceTables : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // Create Recurrence table
        migrationBuilder.CreateTable(
            name: "Recurrences",
            columns: table => new
            {
                Id = table.Column<int>(nullable: false)
                    .Annotation("Sqlite:Autoincrement", true),
                EventId = table.Column<int>(nullable: false),
                RecurrencePattern = table.Column<string>(nullable: false),
                RecurrenceEndDate = table.Column<DateTime>(nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_Recurrences", x => x.Id);
                table.ForeignKey(
                    name: "FK_Recurrences_Events_EventId",
                    column: x => x.EventId,
                    principalTable: "Events",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        // Create ExclusionDates table
        migrationBuilder.CreateTable(
            name: "ExclusionDates",
            columns: table => new
            {
                Id = table.Column<int>(nullable: false)
                    .Annotation("Sqlite:Autoincrement", true),
                RecurrenceId = table.Column<int>(nullable: false),
                Date = table.Column<DateTime>(nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ExclusionDates", x => x.Id);
                table.ForeignKey(
                    name: "FK_ExclusionDates_Recurrences_RecurrenceId",
                    column: x => x.RecurrenceId,
                    principalTable: "Recurrences",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateIndex(
            name: "IX_Recurrences_EventId",
            table: "Recurrences",
            column: "EventId");

        migrationBuilder.CreateIndex(
            name: "IX_ExclusionDates_RecurrenceId",
            table: "ExclusionDates",
            column: "RecurrenceId");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "ExclusionDates");

        migrationBuilder.DropTable(
            name: "Recurrences");
    }
}