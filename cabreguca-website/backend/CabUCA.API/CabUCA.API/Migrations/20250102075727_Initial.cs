﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace CabUCA.API.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Events",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Title = table.Column<string>(type: "TEXT", nullable: false),
                    Date = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Description = table.Column<string>(type: "TEXT", nullable: false),
                    Category = table.Column<string>(type: "TEXT", nullable: false),
                    LikeCount = table.Column<int>(type: "INTEGER", nullable: false),
                    ImageURL = table.Column<string>(type: "TEXT", nullable: false),
                    URL = table.Column<int>(type: "INTEGER", nullable: false),
                    EnteredBy = table.Column<string>(type: "TEXT", nullable: false),
                    EnteredDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedBy = table.Column<string>(type: "TEXT", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Visibility = table.Column<int>(type: "INTEGER", nullable: false),
                    ViewCount = table.Column<int>(type: "INTEGER", nullable: false),
                    ParticipantCount = table.Column<int>(type: "INTEGER", nullable: false),
                    Location = table.Column<string>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Events", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "Events",
                columns: new[] { "Id", "Category", "Date", "Description", "EnteredBy", "EnteredDate", "ImageURL", "LikeCount", "Location", "ParticipantCount", "Title", "URL", "UpdatedBy", "UpdatedDate", "ViewCount", "Visibility" },
                values: new object[,]
                {
                    { 1, "Service", new DateTime(2025, 1, 14, 9, 30, 0, 0, DateTimeKind.Unspecified), "Join us for our weekly Sunday morning service with Pastor John Smith.", "admin", new DateTime(2025, 1, 1, 12, 0, 0, 0, DateTimeKind.Unspecified), "https://example.com/images/sunday-service.jpg", 15, "Main Sanctuary, Caboolture Uniting Church", 80, "Sunday Morning Service", 1001, "admin", new DateTime(2025, 1, 1, 12, 0, 0, 0, DateTimeKind.Unspecified), 45, 1 },
                    { 2, "Education", new DateTime(2025, 1, 16, 18, 30, 0, 0, DateTimeKind.Unspecified), "Weekly youth Bible study focusing on the Book of Acts.", "youth_leader", new DateTime(2025, 1, 2, 9, 0, 0, 0, DateTimeKind.Unspecified), "https://example.com/images/youth-study.jpg", 8, "Youth Room, Caboolture Uniting Church", 15, "Youth Bible Study", 1002, "youth_leader", new DateTime(2025, 1, 2, 9, 0, 0, 0, DateTimeKind.Unspecified), 25, 1 },
                    { 3, "Community", new DateTime(2025, 1, 20, 10, 0, 0, 0, DateTimeKind.Unspecified), "Monthly community food drive to support local families in need.", "outreach_coordinator", new DateTime(2025, 1, 3, 14, 30, 0, 0, DateTimeKind.Unspecified), "https://example.com/images/food-drive.jpg", 32, "Church Parking Lot, Caboolture Uniting Church", 40, "Community Food Drive", 1003, "outreach_coordinator", new DateTime(2025, 1, 5, 16, 45, 0, 0, DateTimeKind.Unspecified), 120, 1 },
                    { 4, "Prayer", new DateTime(2025, 1, 17, 19, 0, 0, 0, DateTimeKind.Unspecified), "Mid-week prayer meeting for our church and community needs.", "prayer_team_leader", new DateTime(2025, 1, 4, 10, 15, 0, 0, DateTimeKind.Unspecified), "https://example.com/images/prayer-meeting.jpg", 12, "Prayer Room, Caboolture Uniting Church", 20, "Prayer Meeting", 1004, "prayer_team_leader", new DateTime(2025, 1, 4, 10, 15, 0, 0, DateTimeKind.Unspecified), 30, 1 },
                    { 5, "Music", new DateTime(2025, 1, 18, 18, 30, 0, 0, DateTimeKind.Unspecified), "Weekly worship team rehearsal for Sunday service.", "worship_leader", new DateTime(2025, 1, 5, 8, 0, 0, 0, DateTimeKind.Unspecified), "https://example.com/images/worship-practice.jpg", 5, "Main Sanctuary, Caboolture Uniting Church", 10, "Worship Team Practice", 1005, "worship_leader", new DateTime(2025, 1, 5, 8, 0, 0, 0, DateTimeKind.Unspecified), 15, 0 }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Events");
        }
    }
}
