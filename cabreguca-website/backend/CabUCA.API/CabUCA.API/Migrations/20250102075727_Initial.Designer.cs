﻿// <auto-generated />
using System;
using CabUCA.API.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CabUCA.API.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250102075727_Initial")]
    partial class Initial
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.0");

            modelBuilder.Entity("CabUCA.API.Models.Event", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Date")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("EnteredBy")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("EnteredDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("ImageURL")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("LikeCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("ParticipantCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("URL")
                        .HasColumnType("INTEGER");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.Property<int>("ViewCount")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Visibility")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("Events");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Category = "Service",
                            Date = new DateTime(2025, 1, 14, 9, 30, 0, 0, DateTimeKind.Unspecified),
                            Description = "Join us for our weekly Sunday morning service with Pastor John Smith.",
                            EnteredBy = "admin",
                            EnteredDate = new DateTime(2025, 1, 1, 12, 0, 0, 0, DateTimeKind.Unspecified),
                            ImageURL = "https://example.com/images/sunday-service.jpg",
                            LikeCount = 15,
                            Location = "Main Sanctuary, Caboolture Uniting Church",
                            ParticipantCount = 80,
                            Title = "Sunday Morning Service",
                            URL = 1001,
                            UpdatedBy = "admin",
                            UpdatedDate = new DateTime(2025, 1, 1, 12, 0, 0, 0, DateTimeKind.Unspecified),
                            ViewCount = 45,
                            Visibility = 1
                        },
                        new
                        {
                            Id = 2,
                            Category = "Education",
                            Date = new DateTime(2025, 1, 16, 18, 30, 0, 0, DateTimeKind.Unspecified),
                            Description = "Weekly youth Bible study focusing on the Book of Acts.",
                            EnteredBy = "youth_leader",
                            EnteredDate = new DateTime(2025, 1, 2, 9, 0, 0, 0, DateTimeKind.Unspecified),
                            ImageURL = "https://example.com/images/youth-study.jpg",
                            LikeCount = 8,
                            Location = "Youth Room, Caboolture Uniting Church",
                            ParticipantCount = 15,
                            Title = "Youth Bible Study",
                            URL = 1002,
                            UpdatedBy = "youth_leader",
                            UpdatedDate = new DateTime(2025, 1, 2, 9, 0, 0, 0, DateTimeKind.Unspecified),
                            ViewCount = 25,
                            Visibility = 1
                        },
                        new
                        {
                            Id = 3,
                            Category = "Community",
                            Date = new DateTime(2025, 1, 20, 10, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Monthly community food drive to support local families in need.",
                            EnteredBy = "outreach_coordinator",
                            EnteredDate = new DateTime(2025, 1, 3, 14, 30, 0, 0, DateTimeKind.Unspecified),
                            ImageURL = "https://example.com/images/food-drive.jpg",
                            LikeCount = 32,
                            Location = "Church Parking Lot, Caboolture Uniting Church",
                            ParticipantCount = 40,
                            Title = "Community Food Drive",
                            URL = 1003,
                            UpdatedBy = "outreach_coordinator",
                            UpdatedDate = new DateTime(2025, 1, 5, 16, 45, 0, 0, DateTimeKind.Unspecified),
                            ViewCount = 120,
                            Visibility = 1
                        },
                        new
                        {
                            Id = 4,
                            Category = "Prayer",
                            Date = new DateTime(2025, 1, 17, 19, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Mid-week prayer meeting for our church and community needs.",
                            EnteredBy = "prayer_team_leader",
                            EnteredDate = new DateTime(2025, 1, 4, 10, 15, 0, 0, DateTimeKind.Unspecified),
                            ImageURL = "https://example.com/images/prayer-meeting.jpg",
                            LikeCount = 12,
                            Location = "Prayer Room, Caboolture Uniting Church",
                            ParticipantCount = 20,
                            Title = "Prayer Meeting",
                            URL = 1004,
                            UpdatedBy = "prayer_team_leader",
                            UpdatedDate = new DateTime(2025, 1, 4, 10, 15, 0, 0, DateTimeKind.Unspecified),
                            ViewCount = 30,
                            Visibility = 1
                        },
                        new
                        {
                            Id = 5,
                            Category = "Music",
                            Date = new DateTime(2025, 1, 18, 18, 30, 0, 0, DateTimeKind.Unspecified),
                            Description = "Weekly worship team rehearsal for Sunday service.",
                            EnteredBy = "worship_leader",
                            EnteredDate = new DateTime(2025, 1, 5, 8, 0, 0, 0, DateTimeKind.Unspecified),
                            ImageURL = "https://example.com/images/worship-practice.jpg",
                            LikeCount = 5,
                            Location = "Main Sanctuary, Caboolture Uniting Church",
                            ParticipantCount = 10,
                            Title = "Worship Team Practice",
                            URL = 1005,
                            UpdatedBy = "worship_leader",
                            UpdatedDate = new DateTime(2025, 1, 5, 8, 0, 0, 0, DateTimeKind.Unspecified),
                            ViewCount = 15,
                            Visibility = 0
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
