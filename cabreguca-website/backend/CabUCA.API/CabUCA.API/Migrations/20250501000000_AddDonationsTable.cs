using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CabUCA.API.Migrations
{
    /// <summary>
    /// Migration to add the Donations table
    /// </summary>
    public partial class AddDonationsTable : Migration
    {
        /// <summary>
        /// Creates the Donations table
        /// </summary>
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Donations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    AmountInCents = table.Column<long>(type: "INTEGER", nullable: false),
                    Currency = table.Column<string>(type: "TEXT", maxLength: 3, nullable: false),
                    DonationDate = table.Column<DateTime>(type: "TEXT", nullable: false),
                    Purpose = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    IsAnonymous = table.Column<bool>(type: "INTEGER", nullable: false),
                    DonorName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    DonorEmail = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    DonorMessage = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    PaymentIntentId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    PaymentStatus = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                    UserId = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Donations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Donations_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Donations_UserId",
                table: "Donations",
                column: "UserId");
        }

        /// <summary>
        /// Drops the Donations table
        /// </summary>
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Donations");
        }
    }
}
