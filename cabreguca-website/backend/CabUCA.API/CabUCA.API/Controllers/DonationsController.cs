using CabUCA.API.Dtos.Donations;
using CabUCA.API.Interfaces;
using CabUCA.API.Mappers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using System.Security.Claims;

namespace CabUCA.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DonationsController : ControllerBase
    {
        private readonly IDonationRepository _donationRepository;
        private readonly IStripeService _stripeService;
        private readonly ILogger<DonationsController> _logger;

        public DonationsController(
            IDonationRepository donationRepository,
            IStripeService stripeService,
            ILogger<DonationsController> logger)
        {
            _donationRepository = donationRepository;
            _stripeService = stripeService;
            _logger = logger;
        }

        /// <summary>
        /// Creates a payment intent for a donation
        /// </summary>
        /// <param name="createDonationDto">The donation information</param>
        /// <returns>Payment intent information</returns>
        [HttpPost("create-payment-intent")]
        public async Task<IActionResult> CreatePaymentIntent([FromBody] CreateDonationDto createDonationDto)
        {
            try
            {
                // If the user is authenticated, associate the donation with their account
                if (User.Identity?.IsAuthenticated == true)
                {
                    var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                    if (!string.IsNullOrEmpty(userId))
                    {
                        // The donation will be associated with the user in the StripeService
                    }
                }

                var paymentIntentDto = await _stripeService.CreatePaymentIntentAsync(createDonationDto);
                return Ok(paymentIntentDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment intent");
                return StatusCode(500, "An error occurred while processing your donation. Please try again.");
            }
        }

        /// <summary>
        /// Handles webhook events from Stripe
        /// </summary>
        /// <returns>OK if the webhook was processed successfully</returns>
        [HttpPost("webhook")]
        public async Task<IActionResult> HandleWebhook()
        {
            try
            {
                var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
                var signatureHeader = Request.Headers["Stripe-Signature"];

                var success = await _stripeService.ProcessWebhookAsync(json, signatureHeader);
                if (success)
                {
                    return Ok();
                }
                else
                {
                    return BadRequest("Webhook processing failed");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing webhook");
                return StatusCode(500, "An error occurred while processing the webhook.");
            }
        }

        /// <summary>
        /// Gets all donations (for authenticated users)
        /// </summary>
        /// <returns>A list of donations</returns>
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetAllDonations()
        {
            try
            {
                var donations = await _donationRepository.GetAllDonationsAsync();
                var donationDtos = donations.Select(d => d.ToDonationDto());
                return Ok(donationDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all donations");
                return StatusCode(500, "An error occurred while retrieving donations.");
            }
        }

        /// <summary>
        /// Gets a donation by ID (admin only)
        /// </summary>
        /// <param name="id">The donation ID</param>
        /// <returns>The donation if found</returns>
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> GetDonationById(int id)
        {
            try
            {
                var donation = await _donationRepository.GetDonationByIdAsync(id);
                if (donation == null)
                {
                    return NotFound($"Donation with ID {id} not found");
                }

                return Ok(donation.ToDonationDto());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving donation with ID: {id}");
                return StatusCode(500, "An error occurred while retrieving the donation.");
            }
        }

        /// <summary>
        /// Gets donations for the authenticated user
        /// </summary>
        /// <returns>A list of the user's donations</returns>
        [HttpGet("my-donations")]
        [Authorize]
        public async Task<IActionResult> GetMyDonations()
        {
            try
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                var donations = await _donationRepository.GetDonationsByUserIdAsync(userId);
                var donationDtos = donations.Select(d => d.ToDonationDto());
                return Ok(donationDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user donations");
                return StatusCode(500, "An error occurred while retrieving your donations.");
            }
        }
    }
}
