// backend/CabUCA.API/Controllers/EventsController.cs
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using CabUCA.API.Data;
using CabUCA.API.Models;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using CabUCA.API.Dtos.Events;
using CabUCA.API.Services;
using System;
using System.Linq;
using System.Globalization;

namespace CabUCA.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EventsController : ControllerBase
    {
        private readonly AppDbContext _context;
        private readonly RecurrenceService _recurrenceService;

        public EventsController(AppDbContext context, RecurrenceService recurrenceService)
        {
            _context = context;
            _recurrenceService = recurrenceService;
        }

        // GET: api/Events
        /// <summary>
        /// Public frontend endpoint to retrieve events. Frontend use Only.
        /// This endpoint will clone recurring events based on the specified month and year.
        /// if the event has been declared as recurring. Recurring event data (date ranges)
        /// in the Recurring Table will be used to generate the recurring events.
        /// </summary>
        /// <param name="month"></param>
        /// <param name="year"></param>
        /// <returns>200 OK</returns>
        [HttpGet]
        [AllowAnonymous] // Allow public access
        [ResponseCache(Duration = 60, Location = ResponseCacheLocation.Any, NoStore = false)]
        public async Task<ActionResult<CalendarDataDto>> GetEvents([FromQuery] int? month, [FromQuery] int? year)
        {
            if (!month.HasValue || !year.HasValue)
            {
                return BadRequest("Month and Year parameters are required.");
            }

            var eventInstances = await GenerateEventInstances(month.Value, year.Value);
            var masterEvents = await GetMasterEventsForMonth(month.Value, year.Value);

            var calendarData = new CalendarDataDto
            {
                EventInstances = eventInstances,
                MasterEvents = masterEvents
            };

            return Ok(calendarData);
        }

        /// <summary>
        ///  The Admin Endpoint to List Events on Events Page (administrators frontend)
        /// Admin Frontend only
        /// </summary>
        /// <returns>200 OK</returns>
        [HttpGet("admin")]
        public async Task<ActionResult<IEnumerable<EventDto>>> GetAdminEvents()
        {
          var events = await _context.Events
              .Include(e => e.Recurrence)
                  .ThenInclude(r => r.ExclusionDates)
              .ToListAsync();

          var eventDtos = events.Select(e => new EventDto
          {
              Id = e.Id,
              Title = e.Title,
              Date = e.Date,
              Description = e.Description,
              Category = e.Category,
              Location = e.Location,
              ImageURL = e.ImageURL,
              URL = e.URL,
              Visibility = e.Visibility,
              RecurrencePattern = e.Recurrence?.RecurrencePattern,
              RecurrenceEndDate = e.Recurrence?.RecurrenceEndDate,
              ExclusionDates = e.Recurrence?.ExclusionDates.Select(ed => ed.Date).ToList()
          }).ToList();

          return Ok(eventDtos);
        }

        // GET: api/Events/5
        /// <summary>
        /// Public Frontend Endpoint to retrieve specific event (overlay or
        /// event details. Public Frontend only (no roles needed)
        /// </summary>
        /// <param name="id"></param>
        /// <returns>event data transfer object</returns>
        [HttpGet("{id}")]
        [AllowAnonymous] // Allow public access
        public async Task<ActionResult<EventDto>> GetEventById(int id)
        {
            var @event = await _context.Events
                .Include(e => e.Recurrence)
                    .ThenInclude(r => r.ExclusionDates)
                .FirstOrDefaultAsync(e => e.Id == id);

            if (@event == null)
            {
                return NotFound();
            }

            var eventDto = new EventDto
            {
                Id = @event.Id,
                Title = @event.Title,
                Date = @event.Date,
                Description = @event.Description,
                Category = @event.Category,
                Location = @event.Location,
                ImageURL = @event.ImageURL,
                URL = @event.URL,
                ParticipantCount = @event.ParticipantCount,
                EnteredBy = @event.EnteredBy,
                EnteredDate = @event.EnteredDate,
                UpdatedBy = @event.UpdatedBy,
                UpdatedDate = @event.UpdatedDate,
                Visibility = @event.Visibility,
                ViewCount = @event.ViewCount,
                LikeCount = @event.LikeCount,
                RecurrencePattern = @event.Recurrence?.RecurrencePattern,
                RecurrenceEndDate = @event.Recurrence?.RecurrenceEndDate,
                ExclusionDates = @event.Recurrence?.ExclusionDates.Select(ed => ed.Date).ToList()
            };

            return eventDto;
        }

        // POST: api/Events
        /// <summary>
        /// Coordinator, Admin, Executive Admin endpoint to create new events
        /// Admin Frontend only (elevated privileges required)
        /// </summary>
        /// <param name="createEventDto"></param>
        /// <returns>201 Created</returns>
        [HttpPost]
        [Authorize(Roles = "Coordinator,Admin,ExecutiveAdmin")]
        public async Task<ActionResult<EventDto>> CreateEvent(CreateEventDto createEventDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var username = User.Identity?.Name ?? "Unknown";

            // Manual mapping from CreateEventDto to Event entity
            var @event = new Event
            {
                Title = createEventDto.Title,
                Date = createEventDto.Date,
                Description = createEventDto.Description,
                Category = createEventDto.Category,
                Location = createEventDto.Location,
                ImageURL = createEventDto.ImageURL,
                URL = createEventDto.URL,
                ParticipantCount = createEventDto.ParticipantCount,
                EnteredBy = username,
                EnteredDate = DateTime.UtcNow,
                UpdatedBy = username,
                UpdatedDate = DateTime.UtcNow,
                Visibility = createEventDto.Visibility,
                ViewCount = createEventDto.ViewCount,
                LikeCount = createEventDto.LikeCount
            };

            // Handle Recurrence Logic
            if (!string.IsNullOrEmpty(createEventDto.RecurrencePattern))
            {
                var recurrence = new Recurrence
                {
                    RecurrencePattern = createEventDto.RecurrencePattern,
                    RecurrenceEndDate = createEventDto.RecurrenceEndDate,
                    Event = @event
                };

                @event.Recurrence = recurrence;

                if (createEventDto.ExclusionDates != null && createEventDto.ExclusionDates.Any())
                {
                    recurrence.ExclusionDates = createEventDto.ExclusionDates.Select(ed => new ExclusionDate
                    {
                        Date = ed,
                        Recurrence = recurrence
                    }).ToList();
                }
            }

            _context.Events.Add(@event);

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateException)
            {
                // Log the exception details here (e.g., using a logging framework)
                return StatusCode(500, "An error occurred while creating the event.");
            }

            // Manual mapping from Event entity to EventDto
            var eventDto = new EventDto
            {
              // maye need to tweak ID as should be auto generated:
                Id = @event.Id,
                Title = @event.Title,
                Date = @event.Date,
                Description = @event.Description,
                Category = @event.Category,
                Location = @event.Location,
                ImageURL = @event.ImageURL,
                URL = @event.URL,
                ParticipantCount = @event.ParticipantCount,
                EnteredBy = @event.EnteredBy,
                EnteredDate = @event.EnteredDate,
                UpdatedBy = @event.UpdatedBy,
                UpdatedDate = @event.UpdatedDate,
                Visibility = @event.Visibility,
                ViewCount = @event.ViewCount,
                LikeCount = @event.LikeCount,
                RecurrencePattern = @event.Recurrence?.RecurrencePattern,
                RecurrenceEndDate = @event.Recurrence?.RecurrenceEndDate,
                ExclusionDates = @event.Recurrence?.ExclusionDates.Select(ed => ed.Date).ToList()
            };

            return CreatedAtAction(nameof(GetEventById), new { id = @event.Id }, eventDto);
        }

        // PUT: api/Events/5
        /// <summary>
        /// Update Event:
        /// Coordinator, Admin, Executive Admin endpoint to update events
        /// Admin Frontend only (elevated privileges required)
        /// </summary>
        /// <param name="id"></param>
        /// <param name="updateEventDto"></param>
        /// <returns>Update (No Content)</returns>
        [HttpPut("{id}")]
        [Authorize(Roles = "Coordinator,Admin,ExecutiveAdmin")] // Require authentication
        public async Task<IActionResult> UpdateEvent(int id, UpdateEventDto updateEventDto)
        {
            if (!ModelState.IsValid)
            {
                // Extract and return detailed validation errors
                var errors = ModelState.Values.SelectMany(v => v.Errors)
                                              .Select(e => e.ErrorMessage)
                                              .ToList();
                return BadRequest(new { Message = "Validation errors occurred.", Errors = errors });
            }

            var @event = await _context.Events
                .Include(e => e.Recurrence)
                    .ThenInclude(r => r.ExclusionDates)
                .FirstOrDefaultAsync(e => e.Id == id);

            if (@event == null)
            {
                return NotFound();
            }

            // Update properties
            @event.Title = updateEventDto.Title;
            @event.Date = updateEventDto.Date;
            @event.Description = updateEventDto.Description;
            @event.Category = updateEventDto.Category;
            @event.Location = updateEventDto.Location;
            @event.ImageURL = updateEventDto.ImageURL;
            @event.URL = updateEventDto.URL;
            @event.ParticipantCount = updateEventDto.ParticipantCount;
            @event.UpdatedBy = User.Identity?.Name ?? "Unknown";
            @event.Visibility = updateEventDto.Visibility;

            _context.Entry(@event).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();

                // Handle Recurrence
                if (!string.IsNullOrEmpty(updateEventDto.RecurrencePattern))
                {
                    if (@event.Recurrence == null)
                    {
                        // Create new Recurrence
                        var newRecurrence = new Recurrence
                        {
                            EventId = @event.Id,
                            RecurrencePattern = updateEventDto.RecurrencePattern,
                            RecurrenceEndDate = updateEventDto.RecurrenceEndDate
                        };
                        _context.Recurrences.Add(newRecurrence);
                        await _context.SaveChangesAsync();

                        // Handle Exclusion Dates
                        if (updateEventDto.ExclusionDates != null && updateEventDto.ExclusionDates.Any())
                        {
                            foreach (var exDate in updateEventDto.ExclusionDates)
                            {
                                var exclusion = new ExclusionDate
                                {
                                    RecurrenceId = newRecurrence.Id,
                                    Date = exDate.Date
                                };
                                _context.ExclusionDates.Add(exclusion);
                            }
                            await _context.SaveChangesAsync();
                        }
                    }
                    else
                    {
                        // Update existing Recurrence
                        @event.Recurrence.RecurrencePattern = updateEventDto.RecurrencePattern;
                        @event.Recurrence.RecurrenceEndDate = updateEventDto.RecurrenceEndDate;
                        _context.Entry(@event.Recurrence).State = EntityState.Modified;
                        await _context.SaveChangesAsync();

                        // Handle Exclusion Dates
                        if (updateEventDto.ExclusionDates != null)
                        {
                            // Remove existing Exclusion Dates
                            _context.ExclusionDates.RemoveRange(@event.Recurrence.ExclusionDates);

                            // Add new Exclusion Dates
                            foreach (var exDate in updateEventDto.ExclusionDates)
                            {
                                var exclusion = new ExclusionDate
                                {
                                    RecurrenceId = @event.Recurrence.Id,
                                    Date = exDate.Date
                                };
                                _context.ExclusionDates.Add(exclusion);
                            }
                            await _context.SaveChangesAsync();
                        }
                    }
                }
                else
                {
                    // If recurrence pattern is not provided, remove any existing Recurrence
                    if (@event.Recurrence != null)
                    {
                        _context.ExclusionDates.RemoveRange(@event.Recurrence.ExclusionDates);
                        _context.Recurrences.Remove(@event.Recurrence);
                        await _context.SaveChangesAsync();
                    }
                }
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EventExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/Events/5
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,ExecutiveAdmin")] // Require authentication
        public async Task<IActionResult> DeleteEvent(int id)
        {
            var @event = await _context.Events
                .Include(e => e.Recurrence)
                    .ThenInclude(r => r.ExclusionDates)
                .FirstOrDefaultAsync(e => e.Id == id);

            if (@event == null)
            {
                return NotFound();
            }

            if (@event.Recurrence != null)
            {
                _context.ExclusionDates.RemoveRange(@event.Recurrence.ExclusionDates);
                _context.Recurrences.Remove(@event.Recurrence);
            }

            _context.Events.Remove(@event);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool EventExists(int id)
        {
            return _context.Events.Any(e => e.Id == id);
        }

        // Helper method to generate recurring events
        private List<Event> GenerateRecurringEvents(Event originalEvent, int? month, int? year)
        {
            var instances = new List<Event>();
            var recurrence = originalEvent.Recurrence;
            if (recurrence == null)
                return instances;

            DateTime startDate = originalEvent.Date;
            DateTime endDate = recurrence.RecurrenceEndDate ?? DateTime.Now.AddYears(1); // Default to 1 year if no end date

            // Define the interval based on recurrence pattern
            Func<DateTime, DateTime> getNextDate = recurrence.RecurrencePattern switch
            {
                "Daily" => date => date.AddDays(1),
                "Weekly" => date => date.AddDays(7),
                "Monthly" => date => date.AddMonths(1),
                _ => date => date.AddDays(1), // Default to daily
            };

            var current = startDate;

            while (current <= endDate)
            {
                if ((month.HasValue && current.Month != month.Value) || (year.HasValue && current.Year != year.Value))
                {
                    current = getNextDate(current);
                    continue;
                }

                // Check exclusion dates
                if (recurrence.ExclusionDates != null && recurrence.ExclusionDates.Any(ex => ex.Date.Date == current.Date))
                {
                    current = getNextDate(current);
                    continue;
                }

                // Clone the original event for each instance
                var recurringEvent = new Event
                {
                    // Do NOT set the Id property; let EF handle it
                    Title = originalEvent.Title,
                    Date = current,
                    Description = originalEvent.Description,
                    Category = originalEvent.Category,
                    Location = originalEvent.Location,
                    ImageURL = originalEvent.ImageURL,
                    URL = originalEvent.URL,
                    EnteredBy = originalEvent.EnteredBy,
                    EnteredDate = originalEvent.EnteredDate,
                    UpdatedBy = originalEvent.UpdatedBy,
                    UpdatedDate = originalEvent.UpdatedDate,
                    Visibility = originalEvent.Visibility,
                    ViewCount = originalEvent.ViewCount,
                    ParticipantCount = originalEvent.ParticipantCount
                };

                instances.Add(recurringEvent);
                current = getNextDate(current);
            }

            return instances;
        }

        /// <summary>
        /// Public frontend endpoint to retrieve master events for the specified month and year.
        /// </summary>
        /// <param name="month">1-12 representing the month</param>
        /// <param name="year">Year in four digits</param>
        /// <returns>200 OK with list of master events occurring in the specified month and year</returns>
        [HttpGet("masters")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<EventMasterDto>>> GetMasterEvents([FromQuery] int? month, [FromQuery] int? year)
        {
            if (!month.HasValue || !year.HasValue)
            {
                return BadRequest("Month and Year parameters are required.");
            }

            var targetMonth = month.Value;
            var targetYear = year.Value;

            var events = await _context.Events
                .Include(e => e.Recurrence)
                    .ThenInclude(r => r.ExclusionDates)
                .ToListAsync();

            var filteredMasterEvents = events
                .Where(e => DoesEventRecurInMonth(
                    new EventMasterDto
                    {
                        Id = e.Id,
                        Title = e.Title,
                        Description = e.Description,
                        Category = e.Category,
                        Date = e.Date,
                        Location = e.Location,
                        RecurrencePattern = e.Recurrence?.RecurrencePattern,
                        RecurrenceEndDate = e.Recurrence?.RecurrenceEndDate,
                        DayName = e.Date.ToString("dddd", CultureInfo.InvariantCulture)
                    },
                    targetMonth,
                    targetYear))
                .ToList();

            return Ok(filteredMasterEvents);
        }

        // Helper methods to generate event instances and master events

        /// <summary>
        /// Determines if a master event recurs in the specified month and year.
        /// </summary>
        /// <param name="evt">EventMasterDto object</param>
        /// <param name="targetMonth">Target month (1-12)</param>
        /// <param name="targetYear">Target year</param>
        /// <returns>True if the event recurs in the target month/year; otherwise, false.</returns>
        private bool DoesEventRecurInMonth(EventMasterDto evt, int targetMonth, int targetYear)
        {
            if (string.IsNullOrEmpty(evt.RecurrencePattern))
            {
                // Non-recurring event: Check if its date is in the target month/year
                return evt.Date.Month == targetMonth && evt.Date.Year == targetYear;
            }

            DateTime eventDate = evt.Date;
            DateTime targetDate = new DateTime(targetYear, targetMonth, 1);

            // If the event's start date is after the target month, it doesn't recur here
            if (eventDate > targetDate.AddMonths(1).AddDays(-1))
                return false;

            // If the event has a recurrence end date and it's before the target month, skip
            if (evt.RecurrenceEndDate.HasValue && evt.RecurrenceEndDate.Value < targetDate)
                return false;

            // Implement logic based on recurrence pattern
            // Declare variables needed across multiple cases
            int eventWeekday = (int)eventDate.DayOfWeek;
            DateTime firstOfMonth = new DateTime(targetYear, targetMonth, 1);
            int firstWeekday = (int)firstOfMonth.DayOfWeek;
            int daysOffset = (eventWeekday - firstWeekday + 7) % 7;
            DateTime firstOccurrence = firstOfMonth.AddDays(daysOffset);

            switch (evt.RecurrencePattern.ToLower())
            {
                case "daily":
                    // Daily recurrence: Always recurs if within start and end dates
                    return true;

                case "weekly":
                    // Weekly recurrence: Check if the event occurs on any day in the target month
                    // Assuming recurrence on the same weekday as the start date
                    // eventWeekday = (int)eventDate.DayOfWeek;
                    // firstOfMonth = new DateTime(targetYear, targetMonth, 1);
                    // firstWeekday = (int)firstOfMonth.DayOfWeek;

                    // // Calculate the first occurrence of the event's weekday in the target month
                    // daysOffset = (eventWeekday - firstWeekday + 7) % 7;
                    // firstOccurrence = firstOfMonth.AddDays(daysOffset);

                    // If the first occurrence is within the month and before the recurrence end date
                    if (firstOccurrence.Month == targetMonth && (evt.RecurrenceEndDate == null || firstOccurrence <= evt.RecurrenceEndDate))
                        return true;

                    return false;

                case "fortnightly":
                    // Fortnightly recurrence: Check if the event occurs on a Friday within the target month
                    // Assuming recurrence on the same weekday as the start date

                    // If the first occurrence is within the month and before the recurrence end date
                    if (firstOccurrence.Month == targetMonth && (evt.RecurrenceEndDate == null || firstOccurrence <= evt.RecurrenceEndDate))
                        return true;

                    return false;

                case "monthly":
                    // Monthly recurrence: Check if the event's day exists in the target month
                    int dayOfMonth = eventDate.Day;
                    int daysInTargetMonth = DateTime.DaysInMonth(targetYear, targetMonth);
                    return dayOfMonth <= daysInTargetMonth;

                // Add more recurrence patterns as needed

                default:
                    return false;
            }
        }

        private async Task<List<EventInstanceDto>> GenerateEventInstances(int month, int year)
        {
            var events = await _context.Events
                .Include(e => e.Recurrence)
                    .ThenInclude(r => r.ExclusionDates)
                .ToListAsync();

            var eventInstances = new List<EventInstanceDto>();

            foreach (var evt in events)
            {
                if (evt.Recurrence != null)
                {
                    // Generate recurring instances within the specified month and year
                    var instances = _recurrenceService.GenerateRecurringEvents(evt, month, year);
                    eventInstances.AddRange(instances.Select(i => new EventInstanceDto
                    {
                        Id = i.Id,
                        Title = i.Title,
                        Date = i.Date,
                        Description = i.Description,
                        Category = i.Category,
                        Location = i.Location,
                        ImageURL = i.ImageURL,
                        URL = i.URL,
                        ParticipantCount = i.ParticipantCount
                    }));
                }
                else
                {
                    // Non-recurring event
                    if (month == evt.Date.Month && year == evt.Date.Year)
                    {
                        eventInstances.Add(new EventInstanceDto
                        {
                            Id = evt.Id,
                            Title = evt.Title,
                            Date = evt.Date,
                            Description = evt.Description,
                            Category = evt.Category,
                            Location = evt.Location,
                            ImageURL = evt.ImageURL,
                            URL = evt.URL,
                            ParticipantCount = evt.ParticipantCount
                        });
                    }
                }
            }

            return eventInstances;
        }

        private async Task<List<EventMasterDto>> GetMasterEventsForMonth(int month, int year)
        {
            // Retrieve all events with their recurrence and exclusion dates
            var events = await _context.Events
                .Include(e => e.Recurrence)
                    .ThenInclude(r => r.ExclusionDates)
                .ToListAsync();

            // Filter events that recur in the specified month and year
            var masterEvents = events
                .Where(e => DoesEventRecurInMonth(
                    new EventMasterDto
                    {
                        Id = e.Id,
                        Title = e.Title,
                        Description = e.Description,
                        Category = e.Category,
                        Date = e.Date,
                        Location = e.Location,
                        RecurrencePattern = e.Recurrence?.RecurrencePattern,
                        RecurrenceEndDate = e.Recurrence?.RecurrenceEndDate,
                        DayName = e.Date.ToString("dddd", CultureInfo.InvariantCulture)
                    },
                    month,
                    year))
                // Project the filtered events into EventMasterDto
                .Select(e => new EventMasterDto
                {
                    Id = e.Id,
                    Title = e.Title,
                    Description = e.Description,
                    Category = e.Category,
                    Date = e.Date,
                    Location = e.Location,
                    RecurrencePattern = e.Recurrence?.RecurrencePattern,
                    RecurrenceEndDate = e.Recurrence?.RecurrenceEndDate,
                    DayName = e.Date.ToString("dddd", CultureInfo.InvariantCulture)
                })
                .ToList();

            return masterEvents;
        }
    }
}