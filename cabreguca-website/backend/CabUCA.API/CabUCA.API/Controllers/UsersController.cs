using CabUCA.API.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

[Authorize(Roles = "ExecutiveAdmin")]
[ApiController]
[Route("api/[controller]")]
public class UsersController : ControllerBase
{
    private readonly UserManager<User> _userManager;
    private readonly RoleManager<IdentityRole> _roleManager;

    public UsersController(UserManager<User> userManager, RoleManager<IdentityRole> roleManager)
    {
        _userManager = userManager;
        _roleManager = roleManager;
    }

    // Get all users
    [HttpGet]
    [Authorize(Roles = "Admin, ExecutiveAdmin")]
    public IActionResult GetAllUsers(string searchTerm = "")
    {
        var users = _userManager.Users
            .Where(u => u.UserName.Contains(searchTerm) || u.Email.Contains(searchTerm))
            .Select(u => new
            {
                u.Id,
                u.User<PERSON>ame,
                u.Email
            })
            .ToList();

        return Ok(users);
    }
    
    // Get user roles
    [HttpGet("{userId}/roles")]
    [Authorize(Roles = "ExecutiveAdmin")]
    public async Task<IActionResult> GetUserRoles(string userId)
    {
       var user = await _userManager.FindByIdAsync(userId);
       if (user == null)
           return NotFound();

       var roles = await _userManager.GetRolesAsync(user);
       return Ok(roles);
    } 

    // Get all roles
    [HttpGet("roles")]
    [Authorize(Roles = "ExecutiveAdmin")]
    public IActionResult GetAllRoles()
    {
      var roles = _roleManager.Roles.Select(r => r.Name).ToList();
      return Ok(roles);
    } 

    // Update user roles
   [HttpPost("{userId}/roles")]
   [Authorize(Roles = "ExecutiveAdmin")]
   public async Task<IActionResult> UpdateUserRoles(string userId, [FromBody] List<string> roles)
   {
       var user = await _userManager.FindByIdAsync(userId);
       if (user == null)
           return NotFound();
   
       var currentRoles = await _userManager.GetRolesAsync(user);
       var rolesToAdd = roles.Except(currentRoles).ToList();
       var rolesToRemove = currentRoles.Except(roles).ToList();
   
       var addResult = await _userManager.AddToRolesAsync(user, rolesToAdd);
       var removeResult = await _userManager.RemoveFromRolesAsync(user, rolesToRemove);
   
       if (!addResult.Succeeded || !removeResult.Succeeded)
           return BadRequest("Failed to update user roles.");
   
       return Ok("User roles updated successfully.");
   } 
   
    // Delete user
    [HttpDelete("{userId}")]
    [Authorize(Roles = "ExecutiveAdmin")]
    public async Task<IActionResult> DeleteUser(string userId)
    {
      var user = await _userManager.FindByIdAsync(userId);
      if (user == null)
          return NotFound();

      var result = await _userManager.DeleteAsync(user);
      if (!result.Succeeded)
          return BadRequest("Failed to delete user.");

      return Ok("User deleted successfully.");
    } 

    [HttpGet("current")]
    [Authorize]
    public async Task<IActionResult> GetCurrentUser()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null)
            return Unauthorized();

        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
            return NotFound();

        var roles = await _userManager.GetRolesAsync(user);

        return Ok(new
        {
            userName = user.UserName,
            email = user.Email,
            roles = roles
        });
    }
}