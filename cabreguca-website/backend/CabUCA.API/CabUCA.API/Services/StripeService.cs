using CabUCA.API.Config;
using CabUCA.API.Data;
using CabUCA.API.Dtos.Donations;
using CabUCA.API.Interfaces;
using CabUCA.API.Mappers;
using CabUCA.API.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Stripe;

namespace CabUCA.API.Services
{
    /// <summary>
    /// Service for handling Stripe payment processing
    /// </summary>
    public class StripeService : IStripeService
    {
        private readonly StripeSettings _stripeSettings;
        private readonly AppDbContext _dbContext;
        private readonly ILogger<StripeService> _logger;

        /// <summary>
        /// Initializes a new instance of the StripeService class
        /// </summary>
        public StripeService(
            IOptions<StripeSettings> stripeSettings,
            AppDbContext dbContext,
            ILogger<StripeService> logger)
        {
            _stripeSettings = stripeSettings.Value;
            _dbContext = dbContext;
            _logger = logger;
            
            // Configure Stripe API with the secret key
            StripeConfiguration.ApiKey = _stripeSettings.SecretKey;
        }

        /// <summary>
        /// Creates a payment intent for a donation
        /// </summary>
        public async Task<PaymentIntentDto> CreatePaymentIntentAsync(CreateDonationDto createDonationDto)
        {
            try
            {
                // Create a new donation record
                var donation = createDonationDto.ToDonationEntity();
                _dbContext.Donations.Add(donation);
                await _dbContext.SaveChangesAsync();

                // Create payment intent options
                var options = new PaymentIntentCreateOptions
                {
                    Amount = createDonationDto.AmountInCents,
                    Currency = createDonationDto.Currency.ToLower(),
                    PaymentMethodTypes = new List<string> { "card" },
                    Metadata = new Dictionary<string, string>
                    {
                        { "donation_id", donation.Id.ToString() },
                        { "purpose", createDonationDto.Purpose ?? "General Donation" },
                        { "is_anonymous", createDonationDto.IsAnonymous.ToString() }
                    },
                    Description = $"Donation to CabUCA Church{(string.IsNullOrEmpty(createDonationDto.Purpose) ? "" : $" for {createDonationDto.Purpose}")}"
                };

                // Create the payment intent
                var service = new PaymentIntentService();
                var paymentIntent = await service.CreateAsync(options);

                // Update the donation with the payment intent ID
                donation.PaymentIntentId = paymentIntent.Id;
                await _dbContext.SaveChangesAsync();

                // Return the client secret and publishable key
                return new PaymentIntentDto
                {
                    ClientSecret = paymentIntent.ClientSecret,
                    PublishableKey = _stripeSettings.PublishableKey
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating payment intent");
                throw;
            }
        }

        /// <summary>
        /// Processes a webhook event from Stripe
        /// </summary>
        public async Task<bool> ProcessWebhookAsync(string json, string signatureHeader)
        {
            try
            {
                // Verify the webhook signature
                var stripeEvent = EventUtility.ConstructEvent(
                    json,
                    signatureHeader,
                    _stripeSettings.WebhookSecret
                );

                // Handle the event based on its type
                if (stripeEvent.Type == Events.PaymentIntentSucceeded)
                {
                    var paymentIntent = stripeEvent.Data.Object as PaymentIntent;
                    if (paymentIntent != null)
                    {
                        await UpdateDonationFromPaymentIntentAsync(paymentIntent);
                        _logger.LogInformation($"Payment succeeded for intent: {paymentIntent.Id}");
                    }
                }
                else if (stripeEvent.Type == Events.PaymentIntentPaymentFailed)
                {
                    var paymentIntent = stripeEvent.Data.Object as PaymentIntent;
                    if (paymentIntent != null)
                    {
                        await UpdateDonationFromPaymentIntentAsync(paymentIntent);
                        _logger.LogWarning($"Payment failed for intent: {paymentIntent.Id}");
                    }
                }
                else
                {
                    _logger.LogInformation($"Unhandled event type: {stripeEvent.Type}");
                }

                return true;
            }
            catch (StripeException ex)
            {
                _logger.LogError(ex, "Error processing Stripe webhook");
                return false;
            }
        }

        /// <summary>
        /// Updates a donation based on a payment intent
        /// </summary>
        public async Task<Donation?> UpdateDonationFromPaymentIntentAsync(PaymentIntent paymentIntent)
        {
            try
            {
                // Find the donation by payment intent ID
                var donation = await _dbContext.Donations
                    .FirstOrDefaultAsync(d => d.PaymentIntentId == paymentIntent.Id);

                if (donation == null)
                {
                    _logger.LogWarning($"Donation not found for payment intent: {paymentIntent.Id}");
                    return null;
                }

                // Update the payment status
                donation.PaymentStatus = paymentIntent.Status;
                await _dbContext.SaveChangesAsync();

                return donation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating donation for payment intent: {paymentIntent.Id}");
                return null;
            }
        }
    }
}
