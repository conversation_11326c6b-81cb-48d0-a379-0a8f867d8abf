// backend/CabUCA.API/Program.cs

using System.Globalization;
using CabUCA.API.Config;
using CabUCA.API.Data;
using CabUCA.API.Interfaces;
using CabUCA.API.Models;
using CabUCA.API.Repository;
using CabUCA.API.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Text;
using System.Threading.RateLimiting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.RateLimiting;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.DataProtection;
using System.Security.Cryptography.X509Certificates;
using System.IO;
// using DotNetEnv;


// We no longer load environment variables from .env files
// Instead, we use .NET User Secrets for development and AWS Parameter Store for production

var builder = WebApplication.CreateBuilder(args);

// Update certificate paths in configuration to use actual paths
if (builder.Environment.IsDevelopment())
{
    string homeDir = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);

    // For the development certificate (aspnetapp.pfx)
    string devCertPath = Path.Combine(homeDir, ".aspnet", "https", "aspnetapp.pfx");

    // Update the certificate path in configuration
    if (builder.Configuration["Kestrel:Certificates:Default:Path"] == "DEVELOPMENT_CERT_PATH")
    {
        Console.WriteLine($"Setting development certificate path to: {devCertPath}");
        builder.Configuration["Kestrel:Certificates:Default:Path"] = devCertPath;
    }

    // For data protection keys
    string dataProtectionPath = Path.Combine(homeDir, ".aspnet", "DataProtection-Keys");
    builder.Configuration["DataProtection:KeyPath"] = dataProtectionPath;

    Console.WriteLine($"Development certificate path: {builder.Configuration["Kestrel:Certificates:Default:Path"]}");
    Console.WriteLine($"Data protection path: {builder.Configuration["DataProtection:KeyPath"]}");
}

// Get admin credentials from configuration
string? jwtSecret = builder.Configuration["JwtSettings:Secret"];
var adminUsername = builder.Configuration["AdminSettings:Username"];
var adminEmail = builder.Configuration["AdminSettings:Email"];
var adminPassword = builder.Configuration["AdminSettings:Password"];

// Add configuration sources (for secrets)
builder.Configuration
    .SetBasePath(builder.Environment.ContentRootPath)
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true);

// Add User Secrets in Development, AWS Parameter Store in Production
if (builder.Environment.IsDevelopment())
{
    // User Secrets are automatically loaded in Development
    builder.Configuration.AddUserSecrets<Program>();
    Console.WriteLine("Using User Secrets for configuration in Development");
}
else
{
    // Add AWS Parameter Store in non-development environments
    builder.Configuration.AddSystemsManager("/CabUCA/", optional: true, reloadAfter: TimeSpan.FromMinutes(5));
    Console.WriteLine("Using AWS Parameter Store for configuration in Production");
}

// Always add environment variables as the final override
builder.Configuration.AddEnvironmentVariables();

// Add services to the container:

// Configure DbContext with SQLite
builder.Services.AddDbContext<AppDbContext>(options =>
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection")));

// Add Identity services
builder.Services.AddIdentity<User, IdentityRole>()
    .AddEntityFrameworkStores<AppDbContext>()
    .AddDefaultTokenProviders();

// Configure Identity options
builder.Services.Configure<IdentityOptions>(options =>
{
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireUppercase = true;
    options.Password.RequireNonAlphanumeric = true;
    options.Password.RequiredLength = 8;
    options.Password.RequiredUniqueChars = 4;

    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(15);
    options.Lockout.MaxFailedAccessAttempts = 8;

    // User settings
    options.User.RequireUniqueEmail = true;
    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
});

// Register ApiSettings in Program.cs to use in controllers
builder.Services.Configure<ApiSettings>(builder.Configuration.GetSection("ApiSettings"));
var apiSettings = builder.Configuration.GetSection("ApiSettings").Get<ApiSettings>();

// Register Stripe settings
builder.Services.Configure<StripeSettings>(builder.Configuration.GetSection("StripeSettings"));

// Register CORS services
builder.Services.AddCors(options =>
{
    options.AddPolicy("ProductionPolicy", policy =>
    {
        // WithOrigins requires an exact match of scheme, host, and port.
        // Retrieved from appsettings.json (production values)
        policy.WithOrigins(
            builder.Configuration["ApiSettings:ClientDomain"],   // e.g.: "https://cruca.org"
            builder.Configuration["ApiSettings:AdminDomain"]     // e.g.: "https://admin.cruca.org"
        )
        .AllowAnyHeader()
        .AllowAnyMethod()
        .AllowCredentials();
    });

    // Development CORS policy updated for HTTPS
    options.AddPolicy("DevelopmentCorsPolicy", builder =>
    {
        // Check if we're using HTTPS in development
        var useHttpsInDev = Environment.GetEnvironmentVariable("DEV_HTTPS")?.ToLower() == "true";

        if (useHttpsInDev)
        {
            // Allow both HTTP and HTTPS on localhost and local IP
            builder
                .WithOrigins(
                    "https://localhost:3000",
                    "http://localhost:3000",
                    "https://************:3000",
                    "http://************:3000"
                )
                .AllowAnyMethod()
                .AllowAnyHeader()
                .AllowCredentials();
        }
        else
        {
            // Default localhost policy (HTTP only)
            builder
                .SetIsOriginAllowed(origin =>
                    new Uri(origin).Host == "localhost" ||
                    new Uri(origin).Host == "************")
                .AllowAnyMethod()
                .AllowAnyHeader()
                .AllowCredentials();
        }
    });
});

builder.Services.AddScoped<RecurrenceService>();

// Configure JWT Authentication
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
// TODO why is this variable not used?
// var key = Encoding.ASCII.GetBytes(jwtSecret);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
   if (string.IsNullOrEmpty(jwtSecret))
   {
       throw new InvalidOperationException("Missing JWT_SECRET from configuration. Please set the value in your environment variables or configuration file.");
   }

   options.RequireHttpsMetadata = false;
   options.SaveToken = true;
   options.TokenValidationParameters = new TokenValidationParameters
   {
       ValidateIssuer = true,
       ValidateAudience = true,
       ValidateLifetime = true,
       ValidateIssuerSigningKey = true,
       ValidIssuer = jwtSettings.GetValue<string>("Issuer"),
       ValidAudience = jwtSettings.GetValue<string>("Audience"),
       IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSecret)),
       ClockSkew = TimeSpan.Zero
   };

   // Prevent automatic redirection on unauthorized requests
   options.Events = new JwtBearerEvents
   {
       OnChallenge = context =>
       {
           context.HandleResponse();
           context.Response.StatusCode = 401;
           context.Response.ContentType = "application/json";
           var result = System.Text.Json.JsonSerializer.Serialize(new { message = "Unauthorized" });
           return context.Response.WriteAsync(result);
       },
       OnForbidden = context =>
       {
           context.Response.StatusCode = 403;
           context.Response.ContentType = "application/json";
           var result = System.Text.Json.JsonSerializer.Serialize(new { message = "Forbidden" });
           return context.Response.WriteAsync(result);
       }
   };
});

// Add Authorization
builder.Services.AddAuthorization();

// Register repositories and services
builder.Services.AddScoped<IEventRepository, EventRepository>();
builder.Services.AddScoped<IDonationRepository, DonationRepository>();
builder.Services.AddScoped<IStripeService, StripeService>();

// Add Controllers with JSON options to handle reference loops
// specifically for Event.Recurrence.ExclusionDates which is a list of dates
// which has a reference to the Recurrence object, which has a reference to the Event object (circular reference)
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
        options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
    });

// Add Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "CabUCA API",
        Version = "v1"
    });

    // Define the security scheme
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Name = "Authorization",
        Type = SecuritySchemeType.Http,
        Scheme = "Bearer",
        BearerFormat = "JWT",
        In = ParameterLocation.Header,
        Description = @"JWT Authorization header using the Bearer scheme.
                      Enter 'Bearer' [space] and then your token in the text input below.
                      Example: 'Bearer 12345abcdef'"
    });

    // Add the security requirement to include the bearer scheme globally
    c.AddSecurityRequirement(new OpenApiSecurityRequirement()
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

// TODO
// HTTPS Removed in favour of nginx reverse proxy
// // HTTPS:
// // Add HTTPS configuration
// builder.Services.AddHttpsRedirection(options =>
// {
//     options.RedirectStatusCode = StatusCodes.Status307TemporaryRedirect;
//     options.HttpsPort = 8443; // Your HTTPS port
// });

// // Force HTTPS in production
// if (!builder.Environment.IsDevelopment())
// {
//     builder.Services.AddHsts(options =>
//     {
//         options.Preload = true;
//         options.IncludeSubDomains = true;
//         options.MaxAge = TimeSpan.FromDays(365);
//     });
// }

// Rate Limiting
builder.Services.AddRateLimiter(options =>
{
    options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(context =>
        RateLimitPartition.GetFixedWindowLimiter(
            partitionKey: context.User.Identity?.Name ?? context.Request.Headers.Host.ToString(),
            factory: partition => new FixedWindowRateLimiterOptions
            {
                AutoReplenishment = true,
                PermitLimit = 30,
                Window = TimeSpan.FromMinutes(1)
            }));
});

// Configure Kestrel to listen on HTTP port 8080
builder.WebHost.ConfigureKestrel(options =>
{
    // Check if we're in development mode
    var isDevelopment = builder.Environment.IsDevelopment();
    // Check for the DEV_HTTPS environment variable (true/false)
    var useHttpsInDev = Environment.GetEnvironmentVariable("DEV_HTTPS")?.ToLower() == "true";

    Console.WriteLine($"Development mode: {isDevelopment}, Use HTTPS in Dev: {useHttpsInDev}");

    if (isDevelopment && useHttpsInDev)
    {
        string certPath = builder.Configuration["Kestrel:Certificates:Default:Path"];

        if (!string.IsNullOrEmpty(certPath) && File.Exists(certPath))
        {
            Console.WriteLine($"Using certificate from: {certPath}");
            options.ListenAnyIP(8443, listenOptions =>
            {
                listenOptions.UseHttps(certPath, "");
            });
        }
        else
        {
            Console.WriteLine("WARNING: HTTPS certificate not found. Running in HTTP mode only.");
        }

        // Always listen on HTTP
        options.ListenAnyIP(8080);
    }
    else if (!isDevelopment)
    {
        // Production - let nginx handle SSL termination
        Console.WriteLine("Production mode: HTTP only (SSL termination handled by nginx)");
        options.ListenAnyIP(8080); // HTTP only, nginx handles SSL
    }
    else
    {
        // Development without HTTPS
        Console.WriteLine("Development mode: HTTP only");
        options.ListenAnyIP(8080); // HTTP only
    }
});

// Set the URLs based on the environment
if (builder.Environment.IsDevelopment())
{
    Console.WriteLine("Running in Development mode.");
    var useHttpsInDev = Environment.GetEnvironmentVariable("DEV_HTTPS")?.ToLower() == "true";

    if (useHttpsInDev)
    {
        builder.WebHost.UseUrls("https://localhost:8443", "http://localhost:8080");
    }
    else
    {
        builder.WebHost.UseUrls("http://localhost:8080");
    }
}
else
{
    Console.WriteLine("Running in Production mode.");
    builder.WebHost.UseUrls("http://localhost:8080"); // In production, nginx handles HTTPS
}

// Load the certificate from the specified path
Console.WriteLine("Loading certificate from path: " + builder.Configuration["DATA_PROTECTION_CERT_PATH"]);

X509Certificate2 certificate;
try
{
    // Check if we're in development and DEV_HTTPS is enabled
    var isDevelopment = builder.Environment.IsDevelopment();
    var useHttpsInDev = Environment.GetEnvironmentVariable("DEV_HTTPS")?.ToLower() == "true";

    if (isDevelopment && useHttpsInDev)
    {
        // Get the home directory path for development certificate
        string homeDir = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
        string certPath = Path.Combine(homeDir, ".aspnet", "https", "aspnetapp.pfx");

        if (File.Exists(certPath))
        {
            Console.WriteLine($"Using development certificate for data protection: {certPath}");
            certificate = new X509Certificate2(certPath, "");
        }
        else
        {
            Console.WriteLine("Development certificate not found. Creating a self-signed certificate for data protection.");
            // Create a self-signed certificate for development
            certificate = new X509Certificate2(
                builder.Configuration["DATA_PROTECTION_CERT_PATH"],
                builder.Configuration["DATA_PROTECTION_CERT_PASSWORD"]);
        }
    }
    else
    {
        // Use the configured certificate for production
        certificate = new X509Certificate2(
            builder.Configuration["DATA_PROTECTION_CERT_PATH"],
            builder.Configuration["DATA_PROTECTION_CERT_PASSWORD"]);
    }
}
catch (Exception ex)
{
    Console.WriteLine($"Error loading certificate: {ex.Message}");
    Console.WriteLine("Continuing without certificate-based protection...");

    // Continue without the certificate
    certificate = null;
}

// TODO Ensure the Development cert paths is correc (will be in home/.aspnet/D...)
// TODO Is this block a double up? can it be removed?
if (builder.Environment.IsDevelopment())
{
    Console.WriteLine("Running in development mode, Configuring Data Protection");
    var dataProtection = builder.Services.AddDataProtection()
        .PersistKeysToFileSystem(new DirectoryInfo(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), ".aspnet", "DataProtection-Keys")));

    // Only use certificate if available
    if (certificate != null)
    {
        dataProtection.ProtectKeysWithCertificate(certificate);
    }
}
else
{
    Console.WriteLine("Running in production mode, Configuring Data Protection");
    // Configure Data Protection to use the certificate
    var dataProtection = builder.Services.AddDataProtection()
        .PersistKeysToFileSystem(new DirectoryInfo("/app/.aspnet/DataProtection-Keys"));

    // Only use certificate if available
    if (certificate != null)
    {
        dataProtection.ProtectKeysWithCertificate(certificate);
    }
}

// build the application
var app = builder.Build();

// Apply migrations at startup
using (var scope = app.Services.CreateScope())
{
    var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
    Console.WriteLine("Applying migrations...");
    dbContext.Database.Migrate();
    Console.WriteLine("Migrations applied successfully.");
}

// Set the culture to Australian (need to reflect this in frontend of time conversion occurs)
// TODO: Implement culture settings if required

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "CabUCA API v1");
        c.RoutePrefix = "swagger"; // Set Swagger UI under /swagger
    });

    // Only enable HTTPS redirection in development if DEV_HTTPS is enabled
    var useHttpsInDev = Environment.GetEnvironmentVariable("DEV_HTTPS")?.ToLower() == "true";
    if (useHttpsInDev)
    {
        app.UseHttpsRedirection();
    }
}
else
{
    app.UseCors("ProductionPolicy");
    app.UseHsts();
    app.UseHttpsRedirection();
}

// Use CORS based on environment
if (app.Environment.IsDevelopment())
{
    app.UseCors("DevelopmentCorsPolicy");
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseCors("ProductionPolicy");
    app.UseHsts();
}

// Redirect HTTP to HTTPS - Moved to conditional blocks above
// app.UseHttpsRedirection();

// Set CSP early in the pipeline so it applies to static files as well
app.Use(async (context, next) =>
{
    // Remove any pre-existing CSP header added by earlier middleware, if any.
    context.Response.Headers.Remove("Content-Security-Policy");

    // Build the CSP header value using ApiSettings
    string csp =
        "default-src 'self'; " +
        "img-src 'self' data: https:; " +
        "style-src 'self' 'unsafe-inline' https://unpkg.com; " +
        "script-src 'self' https://kit.fontawesome.com https://unpkg.com 'unsafe-inline' 'unsafe-eval'; " +
        "font-src 'self' https://kit-free.fontawesome.com https://kit.fontawesome.com https://ka-f.fontawesome.com; " +
        "connect-src 'self' " + apiSettings.ApiUrl + " https://ka-f.fontawesome.com;";

    context.Response.Headers.Add("X-Frame-Options", "DENY");
    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Add("Referrer-Policy", "strict-origin");
    context.Response.Headers.Add("Permissions-Policy", "geolocation=(self), microphone=(self), camera=(self)");
    context.Response.Headers.Add("Strict-Transport-Security", "max-age=31536000; includeSubDomains");

    context.Response.Headers.Add("Content-Security-Policy", csp);

    await next();
});

// Serve static files (these now receive the updated header above)
app.UseDefaultFiles();
app.UseStaticFiles();

// Add middleware to inject API URL into api-config.js
app.Use(async (context, next) =>
{
    if (context.Request.Path.Value == "/js/api-config.js")
    {
        var apiUrl = $"{context.Request.Scheme}://{context.Request.Host}/api";

        // Get Stripe publishable key from configuration (User Secrets in dev, Parameter Store in prod)
        var stripePublishableKey = builder.Configuration["StripeSettings:PublishableKey"] ?? "pk_test_placeholder";

        Console.WriteLine($"Injecting API URL: {apiUrl}");
        Console.WriteLine($"Injecting Stripe publishable key: {stripePublishableKey.Substring(0, 10)}...");

        var content = File.ReadAllText(Path.Combine(app.Environment.WebRootPath, "js", "api-config.js"));
        content = content.Replace("{{API_URL}}", apiUrl);
        content = content.Replace("{{STRIPE_PUBLISHABLE_KEY}}", stripePublishableKey);
        context.Response.ContentType = "application/javascript";
        await context.Response.WriteAsync(content);
        return;
    }
    await next();
});

// Fallback to index.html for SPA routes
app.MapFallbackToFile("index.html");

// Authentication & Authorization
app.UseAuthentication();
app.UseAuthorization();

// Map Controllers
app.MapControllers();

// Seed roles and admin user
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();
        var userManager = services.GetRequiredService<UserManager<User>>();
        await SeedRolesAndAdminAsync(roleManager, userManager);
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Seeding failed: {ex.Message}");
    }
}

// Start the application
app.Run();

// Method to seed roles and admin user
async Task SeedRolesAndAdminAsync(RoleManager<IdentityRole> roleManager, UserManager<User> userManager)
{
  var roles = new[] { "User", "Moderator", "Coordinator", "Admin", "ExecutiveAdmin" };

  // Ensure roles are created
  foreach (var role in roles)
  {
      if (!await roleManager.RoleExistsAsync(role))
      {
          var result = await roleManager.CreateAsync(new IdentityRole(role));
          if (!result.Succeeded)
          {
              Console.WriteLine($"Failed to create role '{role}':");
              foreach (var error in result.Errors)
              {
                  Console.WriteLine($"- {error.Description}");
              }
              continue;
          }
          Console.WriteLine($"Role '{role}' created.");
      }
  }

    // Ensure the ExecutiveAdmin role has all permissions.
    // find the user to assign the role to
    var adminUser = await userManager.FindByNameAsync(adminUsername);
    // Create the admin user if it doesn't exist for initial setup
  if (adminUser == null)
  {
    adminUser = new User
    {
        UserName = adminUsername,
        Email = adminEmail,
        EmailConfirmed = true
    };

    var createUserResult = await userManager.CreateAsync(adminUser, adminPassword);

    if (!createUserResult.Succeeded)
    {
        Console.WriteLine("Failed to create admin user:");
        foreach (var error in createUserResult.Errors)
        {
            Console.WriteLine($"- {error.Description}");
        }
        return;
    }
    Console.WriteLine("Admin user created.");

    var addToRoleResult = await userManager.AddToRoleAsync(adminUser, "ExecutiveAdmin");
    if (!addToRoleResult.Succeeded)
    {
        Console.WriteLine("Failed to assign 'ExecutiveAdmin' role to admin user:");
        foreach (var error in addToRoleResult.Errors)
        {
            Console.WriteLine($"- {error.Description}");
        }
        return;
    }
    Console.WriteLine("Admin user assigned to 'ExecutiveAdmin' role.");
  }
  else
  {
    Console.WriteLine("Admin user already exists.");

    // Ensure the user is in the ExecutiveAdmin role
    if (!await userManager.IsInRoleAsync(adminUser, "ExecutiveAdmin"))
    {
      var addToRoleResult = await userManager.AddToRoleAsync(adminUser, "ExecutiveAdmin");
      if (!addToRoleResult.Succeeded)
      {
          Console.WriteLine("Failed to assign 'ExecutiveAdmin' role to existing admin user:");
          foreach (var error in addToRoleResult.Errors)
          {
              Console.WriteLine($"- {error.Description}");
          }
          return;
      }
      Console.WriteLine("Existing admin user assigned to 'ExecutiveAdmin' role.");
    }
    else
    {
        Console.WriteLine("Admin user already has 'ExecutiveAdmin' role.");
    }
  }
}