using CabUCA.API.Dtos.Events;
using CabUCA.API.Models;

namespace CabUCA.API.Mappers;

public static class EventMappers
{
    public static EventDto ToEventDto(this Event eventModel)
    {
        return new EventDto
        {
            Id = eventModel.Id,
            Title = eventModel.Title,
            Date = eventModel.Date,
            Description = eventModel.Description,
            Category = eventModel.Category,
            Location = eventModel.Location
        };
    }

    public static Event ToCreateEventDto(this CreateEventDto eventDto)
    {
        return new Event
        {
            Title = eventDto.Title,
            Date = eventDto.Date,
            Description = eventDto.Description,
            Category = eventDto.Category,
            LikeCount = eventDto.LikeCount,
            ImageURL = eventDto.ImageURL,
            URL = eventDto.URL,
            // EnteredBy = eventDto.EnteredBy,
            EnteredDate = eventDto.EnteredDate,
            UpdatedDate = eventDto.UpdatedDate,
            Visibility = eventDto.Visibility,
            ViewCount = eventDto.ViewCount,
            ParticipantCount = eventDto.ParticipantCount,
            Location = eventDto.Location
        };
    }
    public static UpdateEventDto ToUpdateEventDto(this Event eventDto)
    {
        return new UpdateEventDto()
        {
            Title = eventDto.Title,
            Date = eventDto.Date,
            Description = eventDto.Description,
            Category = eventDto.Category,
            Location = eventDto.Location
        };
    }
}