using CabUCA.API.Dtos.Donations;
using CabUCA.API.Models;

namespace CabUCA.API.Mappers
{
    /// <summary>
    /// Provides mapping methods for Donation entities and DTOs
    /// </summary>
    public static class DonationMappers
    {
        /// <summary>
        /// Maps a Donation entity to a DonationDto
        /// </summary>
        public static DonationDto ToDonationDto(this Donation donation)
        {
            return new DonationDto
            {
                Id = donation.Id,
                AmountInCents = donation.AmountInCents,
                Currency = donation.Currency,
                DonationDate = donation.DonationDate,
                Purpose = donation.Purpose,
                IsAnonymous = donation.IsAnonymous,
                DonorName = donation.IsAnonymous ? null : donation.DonorName,
                DonorEmail = donation.IsAnonymous ? null : donation.DonorEmail,
                DonorMessage = donation.DonorMessage,
                PaymentStatus = donation.PaymentStatus
            };
        }
        
        /// <summary>
        /// Maps a CreateDonationDto to a Donation entity
        /// </summary>
        public static Donation ToDonationEntity(this CreateDonationDto createDonationDto)
        {
            return new Donation
            {
                AmountInCents = createDonationDto.AmountInCents,
                Currency = createDonationDto.Currency,
                DonationDate = DateTime.UtcNow,
                Purpose = createDonationDto.Purpose,
                IsAnonymous = createDonationDto.IsAnonymous,
                DonorName = createDonationDto.IsAnonymous ? null : createDonationDto.DonorName,
                DonorEmail = createDonationDto.IsAnonymous ? null : createDonationDto.DonorEmail,
                DonorMessage = createDonationDto.DonorMessage,
                PaymentStatus = "pending"
            };
        }
    }
}
