public class ApiConfig
{
    public string _environment =
    Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
    public string DomainName =>
     _environment == "Development"
     ? "localhost:3000" 
     : "your-domain.com";
    
    public string ApiDomain => $"api.{DomainName}";
    public string ClientDomain => $"https://{DomainName}";
    public string ApiUrl => $"https://{ApiDomain}";
}

public class ApiSettings
{
    public string ApiUrl { get; set; }
    public string ClientDomain { get; set; }
}