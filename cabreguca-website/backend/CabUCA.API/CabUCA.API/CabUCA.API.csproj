<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UserSecretsId>ab98b1d1-328b-4ce4-9d37-d2c392635bd9</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Amazon.Extensions.Configuration.SystemsManager" Version="6.2.2" />
        <PackageReference Include="DotNetEnv" Version="3.1.1" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.12" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.12" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.11" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.12" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.12" />
        <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.3.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.12">
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
          <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.12">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
     </PackageReference>
        <PackageReference Include="Stripe.net" Version="43.19.0" />
    </ItemGroup>

    <ItemGroup>
      <_ContentIncludedByDefault Remove="wwwroot\__pycache__\server.cpython-313.pyc" />
    </ItemGroup>

    <Target Name="CopyFrontendFiles" BeforeTargets="Build">
        <ItemGroup>
            <FrontendFiles Include="../../../frontend/**/*" Exclude="../../../frontend/.idea/**/*;../../../frontend/.vscode/**/*;../../../frontend/__pycache__/**/*;../../../frontend/server.py" />
        </ItemGroup>
        <Copy SourceFiles="@(FrontendFiles)" DestinationFolder="$(MSBuildProjectDirectory)/wwwroot/%(RecursiveDir)" />
    </Target>

</Project>
