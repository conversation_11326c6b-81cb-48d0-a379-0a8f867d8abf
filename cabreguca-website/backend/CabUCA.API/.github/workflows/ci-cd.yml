name: CI/CD Pipeline

on:
  push:
    branches:
      - main
      - develop
      - 'feature/**'
  pull_request:
    branches:
      - develop

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set up .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0.x'

      - name: Restore Dependencies
        run: dotnet restore backend/CabUCA.API/CabUCA.API.csproj

      - name: Build
        run: dotnet build backend/CabUCA.API/CabUCA.API.csproj --configuration Release --no-restore

      - name: Run Tests
        run: dotnet test backend/CabUCA.API/CabUCA.API.csproj --no-build --verbosity normal

  deploy:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v3
        with:
          push: true
          tags: olmate/cabuca-api:latest

      - name: Deploy to Production Server
        uses: easingthemes/ssh-deploy@v2.1.5
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }} # TODO: Change to your server private key
          remote-user: your-server-username # TODO: Change to your server username
          server-ip: your-server-ip-or-domain # TODO: Change to your server IP or domain
          remote-path: /path/to/your/deployment/directory # TODO: Change to your deployment directory
          script: |
            docker pull your-dockerhub-username/cabucaa-api:latest
            docker stop cabucaa-api || true
            docker rm cabucaa-api || true
            docker run -d --name cabucaa-api -p 8080:8080 your-dockerhub-username/cabucaa-api:latest