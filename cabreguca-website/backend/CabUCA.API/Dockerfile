# Stage 1: Build the application
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy the project file and restore dependencies
COPY ["./CabUCA.API/CabUCA.API.csproj", "./CabUCA.API/"]
RUN dotnet restore "./CabUCA.API/CabUCA.API.csproj"

# Copy the remaining source code and build the project
COPY ./CabUCA.API/ ./CabUCA.API/
WORKDIR "/src/CabUCA.API"
RUN dotnet build "CabUCA.API.csproj" -c Release -o /app/build

# Publish the application
RUN dotnet publish "CabUCA.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Stage 2: Create the runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app

# Create a non-root user for security
RUN adduser --disabled-password \
    --home /app \
    --gecos '' dotnetuser && chown -R dotnetuser /app

# Copy the published application from the build stage
COPY --from=build /app/publish .

# Set ownership and permissions
RUN chown -R dotnetuser:dotnetuser /app

# Create /Data directory and set permissions
RUN mkdir -p /Data && chown -R dotnetuser:dotnetuser /Data

# Create /app/.aspnet/DataProtection-Keys directory and set permissions
RUN mkdir -p /app/.aspnet/DataProtection-Keys && \
    chown -R dotnetuser:dotnetuser /app/.aspnet

# Copy entrypoint script
COPY CabUCA.API/entrypoint.sh .
RUN chmod +x /app/entrypoint.sh

# Switch to the non-root user
USER dotnetuser

# Expose the application port
EXPOSE 8080

# Change entry point (using the entrypoint.sh script)
ENTRYPOINT ["/app/entrypoint.sh"]

# Healthcheck: (request to /health endpoint)
#HEALTHCHECK --interval=30s --timeout=10s \
#  CMD curl -f http://localhost:8080/api/health || exit 1