# .env.example

# WARNING: make sure these values are changed before using the application.
# do not commit this file with password values to the repository, as it contains sensitive information!
# this file is only an example, it is not used by the application itself. Please make sure to create your own .env file.
# (or remove the .example extension from this file.) Make sure this file is in .gitignore (set to ignore .env)

# This file is used to set the environment variables for the application
# It is used by the docker-compose.yml file to set the environment variables for the container
# It is also used by the Dockerfile to set the environment variables for the application
# It is not used by the application itself

# JWT_SECRET=your_secure_jwt_secret_key
# ADMIN_USERNAME=admin
# ADMIN_PASSWORD=your_admin_password
# ADMIN_EMAIL=<EMAIL>
# DOCKER_USERNAME=your_dockerhub_username
# DOCKER_PASSWORD=your_dockerhub_password
# SSH_PRIVATE_KEY=your_ssh_private_key
# SSH_USERNAME=your_ssh_username
# SSH_SERVER_IP=your_ssh_server_ip
# SSH_SERVER_PATH=your_ssh_server_path
# ASPNETCORE_ENVIRONMENT=Production
# ASPNETCORE_URLS=http://+:8080


# .env:

# backend api app environment variables:
ASPNETCORE_ENVIRONMENT=Production
JWT_SECRET=your_secure_jwt_secret_key # change this value

# admin user credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_admin_password # change this value
ADMIN_EMAIL=<EMAIL>

# .env.Production, only if using dockerhub (keep this commented out)
# DOCKER_USERNAME=your-dockerhub-username

# For production, replace this value during deployment
# irellevant if using ecr-credential-helper (recommended) (keep this commented out)
# DOCKER_PASSWORD=your-production-dockerhub-password

# SSH credentials for remote deployment
SSH_PRIVATE_KEY=your-ssh-private-key
SSH_USERNAME=your-ssh-username
SSH_SERVER_IP=your-ssh-server-ip
SSH_SERVER_PATH=your-ssh-server-path

# Data Protection Keys
DATA_PROTECTION_CERT_PASSWORD=your_data_protection_cert_password # change this value
DATA_PROTECTION_CERT_PATH=../certs/dp_keys.pfx

# Let's Encrypt environment variables (for production)
DOMAIN_NAMES=your_domain_name # change this value
EMAIL=<EMAIL>
RSA_KEY_SIZE=4096
STAGING=0
DATA_PATH=./certbot