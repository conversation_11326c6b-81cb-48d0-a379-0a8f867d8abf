#!/bin/bash

# Test script for Stripe integration

echo "Testing Stripe Integration"
echo "=========================="
echo

# Check if Stripe CLI is installed
if ! command -v stripe &> /dev/null; then
    echo "Stripe CLI is not installed. Please install it first:"
    echo "https://stripe.com/docs/stripe-cli#install"
    exit 1
fi

# Start the backend API
echo "Starting the backend API..."
cd backend/CabUCA.API/CabUCA.API
dotnet run &
API_PID=$!

# Wait for the API to start
echo "Waiting for the API to start..."
sleep 10

# Start Stripe webhook forwarding
echo "Starting Stripe webhook forwarding..."
stripe listen --forward-to http://localhost:8080/api/donations/webhook &
STRIPE_PID=$!

# Wait for webhook forwarding to start
sleep 5

# Create a test payment intent
echo "Creating a test payment intent..."
curl -X POST http://localhost:8080/api/donations/create-payment-intent \
  -H "Content-Type: application/json" \
  -d '{
    "amountInCents": 1000,
    "currency": "aud",
    "purpose": "Test Donation",
    "isAnonymous": false,
    "donorName": "Test Donor",
    "donorEmail": "<EMAIL>",
    "donorMessage": "This is a test donation"
  }'

echo
echo "Test payment intent created. Check the API logs for details."
echo "Press Enter to stop the test..."
read

# Clean up
echo "Stopping Stripe webhook forwarding..."
kill $STRIPE_PID

echo "Stopping the backend API..."
kill $API_PID

echo "Test completed."
