server {
    listen 80;
    server_name ${DOMAIN_NAME};

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        autoindex on;
        allow all;
    }

    location / {
        proxy_pass http://api:8080;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

# Include SSL configuration only if the certificate exists
include /etc/nginx/conf.d/ssl.conf;