/**
 * Format a date string into a more readable format
 * @param {string} dateString - The date string to format
 * @returns {string} The formatted date string
 */
export const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-AU', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
};

/**
 * Format a currency amount
 * @param {number} amount - The amount to format
 * @param {string} currency - The currency code (e.g., 'AUD')
 * @returns {string} The formatted currency string
 */
export const formatCurrency = (amount, currency = 'AUD') => {
    return new Intl.NumberFormat('en-AU', {
        style: 'currency',
        currency: currency
    }).format(amount);
};
