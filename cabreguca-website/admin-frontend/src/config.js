const apiDomain = process.env.REACT_APP_API_DOMAIN_NAME || 'localhost:8080';
const baseUrl = process.env.REACT_APP_DOMAIN_NAME || 'localhost:3000';
// Use environment variable to determine protocol or default to HTTP
const apiProtocol = process.env.REACT_APP_API_PROTOCOL || 'http';

const config = {
    api: {
        // Remove domain and baseUrl to use relative paths
        // This allows the frontend to make API calls relative to its own origin
        domain: `${apiProtocol}://${apiDomain}/api/`,
        baseUrl: `http://${baseUrl}/`
    },
    contact: {
        email: process.env.REACT_APP_CONTACT_EMAIL || '<EMAIL>',
        phone: process.env.REACT_APP_CONTACT_PHONE || '07 5499 3411',
        address: process.env.REACT_APP_CONTACT_ADDRESS || 'Caboolture Uniting Church, Corner of King Street and Smiths Road.'
    },
    church: {
        name: process.env.REACT_APP_ORGANISATION_NAME || 'Caboolture UCA',
        fullName: process.env.REACT_APP_ORGANISATION_FULL_NAME || 'Caboolture Uniting Church Australia'
    }
};

export default config;
