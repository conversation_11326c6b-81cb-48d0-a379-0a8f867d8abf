import axios from 'axios';
import { getCurrentUserToken } from './authService';
import config from '../config';

const API_URL = config.api.domain + 'events/';

const headers = () => {
  const token = getCurrentUserToken();
  return {
    headers: {
      Authorization: token ? `Bearer ${token}` : '',
    },
  };
};

export const createEvent = (eventData) => {
  // Make sure the date includes time information
  const eventDate = new Date(eventData.date);
  
  return axios.post(API_URL, {
    title: eventData.title,
    date: eventDate.toISOString(), // This preserves the time component
    description: eventData.description,
    category: eventData.category,
    location: eventData.location,
    imageURL: eventData.imageURL,
    url: eventData.url,
    visibility: eventData.visibility,
    recurrencePattern: eventData.recurrencePattern,
    recurrenceEndDate: eventData.recurrenceEndDate
      ? new Date(eventData.recurrenceEndDate).toISOString()
      : null,
    exclusionDates: eventData.exclusionDates,
  }, headers());
};

export const editEvent = (id, eventData) => {
  // Make sure the date includes time information
  const eventDate = new Date(eventData.date);
  
  return axios.put(`${API_URL}${id}`, {
    title: eventData.title,
    date: eventDate.toISOString(), // This preserves the time component
    description: eventData.description,
    category: eventData.category,
    location: eventData.location,
    imageURL: eventData.imageURL,
    url: eventData.url,
    visibility: eventData.visibility,
    recurrencePattern: eventData.recurrencePattern,
    recurrenceEndDate: eventData.recurrenceEndDate
      ? new Date(eventData.recurrenceEndDate).toISOString()
      : null,
    exclusionDates: eventData.exclusionDates,
  }, headers());
};

export const deleteEvent = (id) => {
  const token = getCurrentUserToken();
  console.log('Token:', token); // Debug token //TODO remove these
  console.log('Delete URL:', `${API_URL}${id}`); // Debug URL //TODO remove these
  
  if (!token) {
    throw new Error('No authentication token found');
  }
  
  return axios.delete(`${API_URL}${id}`, headers())
    .then(response => {
      console.log('Delete successful:', response);
      return response;
    })
    .catch(error => {
      console.error('Delete error:', error);
      throw error;
    });
};
