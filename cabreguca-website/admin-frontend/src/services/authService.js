import axios from 'axios';
import config from '../config';

const API_URL = config.api.domain + 'auth/';

export const register = (username, email, password) => {
  return axios.post(`${API_URL}Register`, { username, email, password });
};

export const login = (username, password) => {
  return axios.post(`${API_URL}Login`, { username, password });
};

export const logout = () => {
  localStorage.removeItem('userToken');
};

export const getCurrentUserToken = () => {
  return localStorage.getItem('userToken');
};

export const getCurrentUser = async () => {
  const token = getCurrentUserToken();
  if (!token) return null;

  try {
    const response = await axios.get(`${API_URL}User`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
    logout();
    return null;
  }
};

export const getCurrentUserDetails = async () => {
  try {
    const token = getCurrentUserToken();
    if (!token) return null;

    const response = await axios.get(`${API_URL}/users/current`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching user details:', error);
    return null;
  }
};