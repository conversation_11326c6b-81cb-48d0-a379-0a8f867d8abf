import React, { useEffect, useState } from 'react';
import { Form, Button, Alert, Row, Col } from 'react-bootstrap';
import { useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import { editEvent } from '../../services/eventService';
import 'bootstrap-icons/font/bootstrap-icons.css';
import config from '../../config';

const API_URL = config.api.domain + 'events/';

function EventEdit() {
  const { id } = useParams();
  const [title, setTitle] = useState('');
  const [date, setDate] = useState('');
  const [time, setTime] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [location, setLocation] = useState('');
  const [imageURL, setImageURL] = useState('');
  const [url, setUrl] = useState('');
  const [visibility, setVisibility] = useState(1);
  const [recurrencePattern, setRecurrencePattern] = useState('');
  const [recurrenceEndDate, setRecurrenceEndDate] = useState('');
  const [exclusionDates, setExclusionDates] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    fetchEvent();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchEvent = async () => {
    try {
      const response = await axios.get(`${API_URL}${id}`);
      const event = response.data;
      setTitle(event.title);
      
      // Split the event date into date and time components
      const eventDate = new Date(event.date);
      setDate(event.date.split('T')[0]); // Date component
      
      // Format time as HH:MM for the time input
      const hours = eventDate.getHours().toString().padStart(2, '0');
      const minutes = eventDate.getMinutes().toString().padStart(2, '0');
      setTime(`${hours}:${minutes}`);
      
      setDescription(event.description);
      setCategory(event.category);
      setLocation(event.location);
      setImageURL(event.imageURL);
      setUrl(event.url);
      setVisibility(event.visibility);
      setRecurrencePattern(event.recurrencePattern || '');
      setRecurrenceEndDate(
        event.recurrenceEndDate
          ? event.recurrenceEndDate.split('T')[0]
          : ''
      );
      setExclusionDates(
        event.exclusionDates ? event.exclusionDates.join(', ') : ''
      );
    } catch (err) {
      setError('Failed to fetch event details.');
      console.error(err);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      // Combine date and time for the datetime value
      const dateTimeString = `${date}T${time}:00+10:00`;  // +10:00 is Brisbane timezone
      const eventDateTime = new Date(dateTimeString);
      
      // Prepare exclusion dates as an array
      const exclusionDatesArray = exclusionDates
        .split(',')
        .map((date) => date.trim())
        .filter((date) => date !== '');

      // Log the data being sent
      console.log('Submitting Event Data:', {
        title,
        date: eventDateTime.toISOString(), // Updated to include time
        description,
        category,
        location,
        imageURL,
        url,
        visibility,
        recurrencePattern,
        recurrenceEndDate,
        exclusionDates: exclusionDatesArray,
      });

      await editEvent(id, {
        title,
        date: eventDateTime.toISOString(), // Updated to include time
        description,
        category,
        location,
        imageURL,
        url,
        visibility,
        recurrencePattern,
        recurrenceEndDate,
        exclusionDates: exclusionDatesArray,
      });
      navigate('/events');
    } catch (err) {
      setError('Failed to update event.');
      console.error(err);
    }
  };

  return (
    <div>
      <Row className="align-items-center mb-4">
        <Col>
            <i className="bi bi-gear me-5 fs-1"></i>
        </Col>
        <Col className="text-center">
          <h2>
            Edit Event
          </h2>
        </Col>
        <Col className="text-end">
          <Button variant="primary" type="submit" className="me-3" form="eventForm">
            Save
          </Button>
          <Button variant="secondary" onClick={() => navigate('/events')}>
            Cancel
          </Button>
        </Col>
      </Row>
      {error && <Alert variant="danger">{error}</Alert>}
      <div className="justify-content-lg-end">
        <small>* indicates required field</small>
      </div>
      <Form onSubmit={handleSubmit} id="eventForm">
        <Row>
          <Col md={8}>
            <Form.Group controlId="formTitle" className="mb-3">
              <Form.Label>Title *</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter event title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </Form.Group>
          </Col>
          <Col md={4}>
            <Row>
              <Col>
                <Form.Group controlId="formDate" className="mb-3">
                  <Form.Label>Date *</Form.Label>
                  <Form.Control
                    type="date"
                    value={date}
                    onChange={(e) => setDate(e.target.value)}
                    required
                  />
                </Form.Group>
              </Col>
              <Col>
                <Form.Group controlId="formTime" className="mb-3">
                  <Form.Label>Time (Brisbane)</Form.Label>
                  <Form.Control
                    type="time"
                    value={time}
                    onChange={(e) => setTime(e.target.value)}
                    required
                  />
                  <Form.Text className="text-muted">
                    Australian Eastern Standard Time
                  </Form.Text>
                </Form.Group>
              </Col>
            </Row>
          </Col>
        </Row>

        <Form.Group controlId="formDescription" className="mb-3">
          <Form.Label>Description</Form.Label>
          <Form.Control
            as="textarea"
            rows={3}
            placeholder="Enter event description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
          />
        </Form.Group>

        <Row>
          <Col md={6}>
            <Form.Group controlId="formCategory" className="mb-3">
              <Form.Label>Category</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter event category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
              />
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group controlId="formLocation" className="mb-3">
              <Form.Label>Location</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter event location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
              />
            </Form.Group>
          </Col>
        </Row>

        <Form.Group controlId="formImageURL" className="mb-3">
          <Form.Label>Image URL</Form.Label>
          <Form.Control
            type="text"
            placeholder="Enter image URL"
            value={imageURL}
            onChange={(e) => setImageURL(e.target.value)}
          />
        </Form.Group>

        <Form.Group controlId="formUrl" className="mb-3">
          <Form.Label>Event URL</Form.Label>
          <Form.Control
            type="text"
            placeholder="Enter event URL"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
          />
        </Form.Group>

        <Row>
          <Col md={6}>
            <Form.Group controlId="formVisibility" className="mb-3">
              <Form.Label>Visibility <br/>
              <small><em><i> (1 is Public, 2 for Church Members, 3 for Moderators, 4 and 5 for Admin)</i></em></small>
              </Form.Label>
              <Form.Control
                  type="number"
                  placeholder="Enter event visibility"
                  value={visibility}
                onChange={(e) => setVisibility(e.target.value)}
              />
            </Form.Group>
          </Col>
          <Col md={6}>
            {/* Recurrence Section */}
            <Form.Label>Recurrence <br/>
            <small><em><i> (If event is recurring, select the recurrence pattern)</i></em></small>
            </Form.Label>
            <Form.Group controlId="formRecurrencePattern" className="mb-3">
              <Form.Select
                value={recurrencePattern}
                onChange={(e) => setRecurrencePattern(e.target.value)}
              >
                <option value="">Select Pattern</option>
                <option value="Daily">Daily</option>
                <option value="Weekly">Weekly</option>
                <option value="Fortnightly">Fortnightly</option>
                <option value="Monthly">Monthly</option>
                {/* Add more patterns as needed */}
              </Form.Select>
            </Form.Group>
          </Col>
        </Row>

        {recurrencePattern && (
          <>
            <Form.Group controlId="formRecurrenceEndDate" className="mb-3">
              <Form.Label>Recurrence End Date</Form.Label>
              <Form.Control
                type="date"
                value={recurrenceEndDate}
                onChange={(e) => setRecurrenceEndDate(e.target.value)}
              />
            </Form.Group>

            <Form.Group controlId="formExclusionDates" className="mb-3">
              <Form.Label>Exclusion Dates</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter dates to exclude (comma-separated, YYYY-MM-DD)"
                value={exclusionDates}
                onChange={(e) => setExclusionDates(e.target.value)}
              />
              <Form.Text className="text-muted">
                <br/>
                Example: (for a Christmas Holiday Break): 2025-12-24, 2025-12-25, 2025-12-26, 2025-12-27, 2025-12-31, 2026-01-01                 <br/>
                Days must be separated by commas and be in the format: YYYY-MM-DD.
                <br/>
                {/* Dates are stored in UTC but will be displayed in Australian Eastern Standard Time (Brisbane). */}
              </Form.Text>
            </Form.Group>
          </>
        )}

        <Button variant="primary" type="submit" className="me-3">
          Save
        </Button>
        <Button variant="secondary" className="ms-3" onClick={() => navigate('/events')}>
          Cancel
        </Button>
      </Form>
    </div>
  );
}

export default EventEdit;
