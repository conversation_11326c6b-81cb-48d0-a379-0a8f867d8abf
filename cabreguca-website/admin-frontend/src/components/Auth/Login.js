import React, { useState } from 'react';
import { login } from '../../services/authService';
import { Form, Button, Alert, Modal } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';

function Login({ onSuccess }) {
  const [form, setForm] = useState({ username: '', password: '' });
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [remainingAttempts, setRemainingAttempts] = useState(null);
  const [lockoutEnd, setLockoutEnd] = useState(null);
  const [, setShowPopup] = useState(true);

  const navigate = useNavigate();
  const handleChange = e => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async e => {
    e.preventDefault();
    try {
      const response = await login(form.username, form.password);
      const token = response.data.token;

      if (token) {
        setMessage('Login successful!');
        setError('');
        setRemainingAttempts(null);
        setLockoutEnd(null);
        setShowPopup(false);
        onSuccess();
        localStorage.setItem('userToken', token);
        navigate('/events');
      }
    } catch (err) {
      setMessage('');
      const errorData = err.response?.data;

      if (errorData?.lockoutEnd) {
        const lockoutEndTime = new Date(errorData.lockoutEnd);
        setError(`Account is locked until ${lockoutEndTime.toLocaleString()}`);
        setLockoutEnd(errorData.lockoutEnd);
        setRemainingAttempts(null);
      } else {
        setError(errorData?.message || 'Login failed.');
        if (errorData?.remainingAttempts !== undefined) {
          setRemainingAttempts(errorData.remainingAttempts);
        } else {
          setRemainingAttempts(null);
        }
        setLockoutEnd(null);
      }
      console.log('Error response:', errorData);
    }
  };

  return (

      <Modal.Body className="bg-dark">
        {message && <Alert variant="success">{message}</Alert>}
        {error && (
          <Alert variant="danger">
            {error}
            {remainingAttempts !== null && (
              <div className="mt-2">
                Remaining attempts: {remainingAttempts}
              </div>
            )}
            {lockoutEnd && (
              <div className="mt-2">
                Please try again after account unlock.
              </div>
            )}
          </Alert>
        )}
        <Form onSubmit={handleSubmit}>
          <Form.Group controlId="formUsername" className="mb-3">
            <Form.Label>Username</Form.Label>
            <Form.Control
              name="username"
              value={form.username}
              onChange={handleChange}
              placeholder="Enter username"
              required
            />
          </Form.Group>
          <Form.Group controlId="formPassword" className="mb-3">
            <Form.Label>Password</Form.Label>
            <Form.Control
              name="password"
              type="password"
              value={form.password}
              onChange={handleChange}
              placeholder="Password"
              required
            />
          </Form.Group>
          <Button variant="primary" type="submit" disabled={lockoutEnd !== null}>
            Login
          </Button>
        </Form>
      </Modal.Body>
  );
}

export default Login;