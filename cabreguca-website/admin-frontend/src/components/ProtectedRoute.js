import React from 'react';
import { Navigate } from 'react-router-dom';
import { getCurrentUserToken } from '../services/authService';

function ProtectedRoute({ children }) {
  // Get the current user's authentication token
  const token = getCurrentUserToken();

  if (!token) {
    return <Navigate to="/" state={{ error: 'Please log in to access this page' }} replace />;
  }

  return children;
}

export default ProtectedRoute;