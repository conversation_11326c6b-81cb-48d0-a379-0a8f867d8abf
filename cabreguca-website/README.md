s# CabUCA Website

Website for Caboolture Uniting Church, featuring a dynamic calendar system.

## Project Structure

- `frontend/` 
	- Static website files (HTML, CSS, JS)
- `admin-frontend/` 
	- Static administrator frontend (React) for CRUD operations and administrative duties
- `backend/` 
	- ASP.NET Core Web API
---
## Development Setup:
---
### Backend

The Kestrel Server is setup to only respond to HTTPS requests, and all HTTP requests are redirected to HTTPS. In order to get the site up and running, SSL Certificates must be generated. This will be done with LetsEncrypt in the production environment. In the Developer environment, self signed certificates are used for testing purposes. 
Nginx is used as a reverse proxy, forwarding TLS traffic to the application, and the application decrypts the traffic. As mentioned, LetsEncrypt manages Certificates in the production environment.

**prerequisites:**
- .Net 8.0
- ASP.NET 8.0
- dotnet entity framework (dotnet-tool)
- Docker
- Docker-compose

To get the server up and running (for a **developer/testing** environment),
**follow these steps:**
1. Generate Self Signed SSL Certificate
2. Create the Entity Framework database migration
3. Update the database
4. set environment variables for secrets using the .env file and the shell script: load-env.sh
5. run docker-compose to create an image of the backend service with the docker **Developer** configuration files

##### **Generate the Self Signed Certificates:**
The certificates should be generated in the `backend/certs` directory, where is is pointed to in Program.cs (application entry point):
```csharp
// Configure Kestrel

builder.WebHost.ConfigureKestrel(options =>
{
// HTTPS with PFX
	options.ListenAnyIP(8443, listenOptions =>
	{	
		listenOptions.UseHttps("../certs/backend.pfx", "your_pfx_password");	
	});
	
// Also listen on HTTP port
options.ListenAnyIP(8000); // Add HTTP endpoint
});
```
*Make sure to replace "your_pfx_password" with the certificate keys import password, or enter an empty string if key is not password protected by removing: your_pfx_password*

To generate self signed certificates using mkcert, enter the following in the terminal:
- Install mkcert

``` bash
# On Ubuntu/Debian

sudo apt install mkcert

# On Arch

sudo pacman -S mkcert

# On macOS

brew install mkcert
```

- Set Up mkcert
    ``` bash
mkcert -install
    ```

- Generate the Certificate
    ``` bash
mkcert localhost
    ```

To generate the .pfx file with your certificate keys:
```bash
openssl pkcs12 -export -out backend.pfx -inkey localhost-key.pem  -in localhost.pem
```
This will satisfy Kestrel Servers SSL configurations, which requires the backend.pfx file. Make sure it is named correctly in accordance to the builder configuration in `Program.cs`:
```csharp
// Program.cs:

// Configure Kestrel
builder.WebHost.ConfigureKestrel(options =>
{
    // HTTPS with PFX
    options.ListenAnyIP(8443, listenOptions =>
    {
        listenOptions.UseHttps("/certs/backend.pfx",
		     "*your_.pfx_password_if_set*");
    });

    // Also listen on HTTP port
    options.ListenAnyIP(8080); // Add HTTP endpoint
});
```

==Check==:

---
```bash
#This will generate localhost.pem and localhost-key.pem. To convert them to .pfx:
openssl pkcs12 -export -out backend.pfx -inkey localhost-key.pem -in localhost.pem

#Set an export password when prompted.
```
---
Again, before running the application you must set the environment variables to create the admin user and set the JWT token.
```bash
cd backend/CabUCA.API

#this will automatically load all of the .env variables that you have set into the environment. Make sure you have set the values, and make sure your passwords are STRONG!
source ./load-env.sh
```

Then you can run the application:
1. Navigate to backend directory:
   ```bash
   cd backend/CabUCA.API
   ```
2. Restore packages:
   ```bash
   dotnet restore
   ```
3. Run the API:
   ```bash
   dotnet run
   ```
---
## Deployment

To deploy the application, use docker-compose in the directory above the backend root directory (where the Dockerfiles are).
Make sure to create self-signed ssl certificates and set the .env file before creating a docker image. Please refer to the admin frontend section for instructions on doing so.

### **Backend**

create a docker image to test the deployment image in developer mode first. There are docker compose files for both production and development environments.

To deploy the image, open a terminal session and enter:

```bash
#navigate to the directory above project root, which contains the docker, and other configuration files 
cd backend/CabUCA.API/

# optionally use the --no-cache flag if you're running into trouble building, which will create a fresh image each time you need to change the config
#--no-cache optional, and slower
docker-compose -f docker-compose.dev.yml build --no-cache 
# this will start and run the entity framework database migration
docker-compose -f docker-compose.dev.yml up
```
Then, in the browser, go to `https://localhost:8443` 

**note**: If you are presented with a warning explaining the risks, make sure you click advanced, then click proceed/accept the risks and continue. Without doing this you will be unable to use the admin panel (admin-frontend) either. All API requests will be blocked due to a certificate authority error.
It is normal to see this risk page because in the development environment, the website uses self signed certificates. In the production environment, it will use Let's Encrypt certificate authorities.

### Frontend
The frontend is hosted with the backend when the ASP.NET core project is built. In the build process, the static frontend (public) files are copied to the `wwwroot/` folder within the `backend/CabUCA.API/CabUCA.API/wwwroot/` folder. The frontend is therefore hosted in the same container.

All that needs to be done to deploy/run the backend and frontend is to execute the commands above (following the Backend setup).

These will be running on port ==8080?==
Type http://localhost:8080 to view the frontend
To test API endpoints, navigate to http://localhost:8080/swagger (in development environment of ASP.NET Core)

---
`ARCHIVE:`
1. ==Navigate to frontend directory:==
   ```bash
   cd frontend
   ```
2. ==Start the development server:==
   ```bash
   python server.py
   or
   python -m server
   ```
3. ==Open browser and navigate to `http://localhost:8000`==

4. ==(optional) Open browser on a mobile device and navigate to `http://[device-ip]:8000`, where `[device-ip]` is the IP address of the device hosting the server (e.g. `*************`).==
--- 
### Admin Frontend

Traffic is also encrypted between NGINX (reverse proxy) and the web services using a self signed certificate.

Before creating a docker image to deploy the web services, configure the .env file with environment secrets and create and SSL certificates for security.

Configure the **.env** file (an example file is in project root):
*navigate (cd in terminal) to /backend/CabUCA.API/*
``` Bash
# .env.example

# WARNING: make sure these values are changed before using the application.
# do not commit this file with password values to the repository, as it contains sensitive information!
# this file is only an example, it is not used by the application itself. Please make sure to create your own .env file.
# (or remove the .example extension from this file.) Make sure this file is in .gitignore (set to ignore .env)

# This file is used to set the environment variables for the application
# It is used by the docker-compose.yml file to set the environment variables for the container
# It is also used by the Dockerfile to set the environment variables for the application
# It is not used by the application itself
JWT_SECRET=your_secure_jwt_secret_key
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_admin_password
ADMIN_EMAIL=<EMAIL>
DOCKER_USERNAME=your_dockerhub_username
DOCKER_PASSWORD=your_dockerhub_password
SSH_PRIVATE_KEY=your_ssh_private_key
SSH_USERNAME=your_ssh_username
SSH_SERVER_IP=your_ssh_server_ip
SSH_SERVER_PATH=your_ssh_server_path
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=http://+:8080

```

*navigate (cd in terminal) to /backend/CabuUCA.API/ and run these commands:*
``` Bash
# From your project root (where docker-compose.prod.yml is located), create a new folder for the certificates

mkdir -p backend/CabUCA.API/certs

cd backend/CabUCA.API/certs

  

# Generate certificates here

openssl genrsa -out backend.key 4096

openssl req -new -key backend.key -out backend.csr

openssl x509 -req -days 365 -in backend.csr -signkey backend.key -out backend.crt
```
Here’s an updated section for your `README.md` that includes the steps for adding an IAM role to the EC2 instance and pulling images with Docker Compose:

---

## **Deployment Guide**

### **1. Set Up the EC2 Instance**

1. **Launch an EC2 Instance**:
   - Go to the AWS EC2 Console and launch a new instance.
   - Choose an appropriate AMI (e.g., Ubuntu Server 24.04 LTS x86_64 is highly recommended).
   - Configure the instance with the necessary resources (e.g., t2.micro for testing. micro is currently sufficient for the application, however as the application grows, a larger instance will be required because of memory usage).

2. **Attach an IAM Role**:
   - Setup an IAM role with the following permissions:
     - AmazonEC2ContainerRegistryReadOnly
     - This will have the following policy (json format):
     ```json
     {
      "Version": "2012-10-17",
      "Statement": [
          {
              "Effect": "Allow",
              "Action": [
                  "ecr:GetAuthorizationToken",
                  "ecr:BatchCheckLayerAvailability",
                  "ecr:GetDownloadUrlForLayer",
                  "ecr:GetRepositoryPolicy",
                  "ecr:DescribeRepositories",
                  "ecr:ListImages",
                  "ecr:DescribeImages",
                  "ecr:BatchGetImage",
                  "ecr:GetLifecyclePolicy",
                  "ecr:GetLifecyclePolicyPreview",
                  "ecr:ListTagsForResource",
                  "ecr:DescribeImageScanFindings"
              ],
              "Resource": "*"
          }
        ]
      } ```
   - In the EC2 Console, select your instance.
   - Click "Actions" → "Security" → "Attach IAM Role".
   - Attach the IAM role (the policy above: AmazonEC2ContainerRegistryReadOnly) to the instance.


3. **SSH into the Instance**:
   ```bash
   ssh -i ~/.ssh/your-key.pem ubuntu@your-instance-ip

   # example:
   # ssh -i ~/.ssh/production-key.pem <EMAIL>
   ```

---

### **2. Install Dependencies**

1. **Install Docker and Docker Compose**:
   ```bash
   sudo apt update
   sudo apt install docker.io docker-compose-plugin
   sudo systemctl enable docker
   sudo systemctl start docker
   ```

2. **Install the Amazon ECR Credential Helper**:
   ```bash
   sudo apt install amazon-ecr-credential-helper
   ```

3. **Configure Docker to Use the ECR Credential Helper**:
   ```bash
   mkdir -p ~/.docker
   echo '{"credsStore": "ecr-login"}' > ~/.docker/config.json
   ```

---

### **3. Clone the Repository**

1. **Clone the Git Repository**:
   ```bash
   git clone https://github.com/your-username/your-repo.git
   cd your-repo
   ```

2. **Pull Docker Configurations and Assets**:
   - Ensure your repository contains the necessary Docker Compose files and assets.

---

### **4. Deploy with Docker Compose**

1. **Pull and Run the Docker Images**:
   ```bash
   docker compose -f docker-compose.yml pull
   docker compose -f docker-compose.yml up -d
   ```

2. **Verify the Deployment**:
   - Check the logs to ensure the services are running:
     ```bash
     docker compose logs
     ```
   - Access the application via the instance’s public IP or domain name.

---

### **5. (Optional) Update the Application**

1. **Pull the Latest Changes**:
    - from github.com/fjord-an/cabuca
   ```bash
   git pull
   ```

2. **Rebuild and Restart the Services**:
   ```bash
   docker compose -f docker-compose.yml down
   docker compose -f docker-compose.yml up -d --build
   ```

---

### **6. Clean Up**

1. **Stop and Remove Containers**:
   ```bash
   docker compose -f docker-compose.yml down
   ```

2. **Remove Unused Images**:
   ```bash
   docker image prune -a
   ```
3. **remove all**
    ``` bash
    docker system prune -a
    ```
5. **remove all volumes**
    ```bash
    docker volume prune
    ```


---

### **Troubleshooting**

- **Permission Issues**: Ensure the IAM role has the correct permissions and is attached to the instance.
- **Docker Errors**: Check Docker logs (`docker compose logs [service-name]`) for errors.
- **ECR Authentication**: Verify the `amazon-ecr-credential-helper` is configured correctly in `~/.docker/config.json`, or if using amazon ec2 instance, ensure the instance has the correct permissions to pull the images:
- in ec2 dashboard, click actions, security, modify IAM role, and attach the role to the instance.

---

This updated guide provides clear steps for setting up an EC2 instance with an IAM role, installing dependencies, and deploying the application using Docker Compose. Let me know if you’d like to add or modify anything!


---
**Archive**
For developer certificates (for testing) use mkcert to create ssl certificates
1. Install mkcert:
   ```bash
   # On Ubuntu/Debian
   sudo apt install mkcert
   # On macOS
   brew install mkcert
   ```
2. Setup certificates:
   ```bash
   cd admin-frontend
   mkcert -install
   mkcert localhost
   ```
3. Start the development server:
   ```bash
   npm start
   ```
4. Open browser and navigate to `https://localhost:3000`

## Database
- SQLite database file is created with dotnet-ef tool
- Migrations are in `backend/CabUCA.API/Data/Migrations`

``` shell
# install dotnet entity framework using nuget
# make sure you also have the dotnet-ef tool:
dotnet-tool ef

# create database migration
dotnet ef migrations add InitialCreate
# generate the database
dotnet ef database update
```

## Development Workflow
1. Make changes
2. Test locally
3. Commit changes:
   ```bash
   git add .
   git commit -m "Description of changes"
   git push
   ```

---
# Production Setup
---
## Backend:
To setup the **Production** environment,
**follow these steps:**
1. Lets Encrypt
2. create a docker image with docker-compose with the production configuration files