

















# Project
- Project: CabUCA Church Website - ASP.NET Core 8 API with React admin panel and vanilla JS frontend, using Stripe for donations and SQLite (planning to migrate to PostgreSQL).
- Purpose: Church website to automate church tasks on a strict budget using free and open source software. The church is charitable and the project is a personal initiative to help them.
- Project Structure: Backend in backend/CabUCA.API/CabUCA.API, frontend in frontend/, admin panel in admin-frontend/. Environment variables loaded from .env files using DotNetEnv. API URL and Stripe keys injected into frontend via api-config.js.

# Tech Stack
- Backend: ASP.NET Core 8 API, Entity Framework Core, JWT authentication.
- Frontend: Vanilla JS for public site, React for admin panel. SPA-like architecture where main layout (navbar/footer) remains static while content is loaded dynamically. Pages like donate.html contain only content without layout elements. Navigation uses data-page attributes instead of direct links.
- Infrastructure: AWS EC2, Docker containers, NGINX, ModSecurity, AWS ECR for image registry.
- Database: Currently using SQLite with plans to migrate to PostgreSQL for better features, data management, and backups. Donations table includes fields for amount, currency, donor info (with anonymous option), purpose, and payment status.

# Stripe Integration
- Donation system using Stripe Elements for payment processing. Backend handles payment intents and webhooks. Donations stored in SQLite database with plans to migrate to PostgreSQL. Admin panel includes donation management interface.

# Environment Configuration
- Development uses local .env files with test keys. Production uses environment variables in Docker containers. API URL and Stripe publishable key injected into frontend at runtime via middleware that replaces placeholders in api-config.js.

# Development Practices
- Test-driven development with tests written first. Clean, maintainable code with meaningful documentation. Async by default unless unnecessary. Error handling only for specific exceptions. Logging using a logger system.
- Code Style: Private class variables start with underscore. Constants use UPPER_SNAKE_CASE. Single quotes for strings. Functions should be small and focused with descriptive names. Documentation should be meaningful and up-to-date.

# Deployment
- Docker containers for services (React admin, API with frontend, NGINX, ModSecurity). Images pushed to AWS ECR and deployed to AWS EC2. Planning to improve deployment, testing, logging, maintenance, and backups.