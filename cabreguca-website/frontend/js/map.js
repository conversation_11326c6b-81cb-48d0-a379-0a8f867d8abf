// Map functionality for the contact page
const MapModule = (function() {
    // Private variables
    const churchCoordinates = {
        caboolture: [-27.077386, 152.931462],
        beachmere: [-27.132810, 153.049988],
        upperCaboolture: [-27.1076402, 152.8948107],
        toganCongregation: [-27.077386, 152.931462],
        unitingcare: [-27.07177982846169, 152.92273469270816]
    };

    // Church information for popups
    const churchInfo = {
        caboolture: 'Caboolture Uniting Church',
        beachmere: 'Beachmere Uniting Church',
        upperCaboolture: 'Upper Caboolture Uniting Church',
        toganCongregation: 'Tongan Congregation Uniting Church',
        unitingcare: 'UnitingCare Caboolture'
    };

    // Function to load Leaflet resources if not already loaded
    function loadLeafletResources(callback) {
        if (typeof L !== 'undefined') {
            // Leaflet already loaded
            callback();
            return;
        }

        console.log('Loading Leaflet resources');
        
        // Load CSS
        const leafletCSS = document.createElement('link');
        leafletCSS.rel = 'stylesheet';
        leafletCSS.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
        document.head.appendChild(leafletCSS);
        
        // Load JavaScript
        const leafletScript = document.createElement('script');
        leafletScript.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
        leafletScript.onload = callback;
        document.body.appendChild(leafletScript);
    }

    // Initialize a specific map by its container ID and location key
    function initializeSpecificMap(mapContainerId, locationKey) {
        const mapContainer = document.getElementById(mapContainerId);
        
        if (!mapContainer) {
            console.error(`Map container ${mapContainerId} not found`);
            return;
        }
        
        try {
            console.log(`Initializing map for ${locationKey}`);
            
            // Clear any fallback content
            mapContainer.innerHTML = '';
            
            // Remove any standalone fallback message
            const fallbackMsg = document.getElementById('map-fallback');
            if (fallbackMsg) {
                fallbackMsg.style.display = 'none';
            }
            
            // Create a map 
            const map = L.map(mapContainerId).setView(churchCoordinates[locationKey], 16);
            
            // Use OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);
            
            // Add a marker
            L.marker(churchCoordinates[locationKey])
                .addTo(map)
                .bindPopup(churchInfo[locationKey])
                .openPopup();
                
            console.log(`Map initialization complete for ${locationKey}`);
            
            // Fix any rendering issues by calling invalidateSize after a delay
            setTimeout(() => map.invalidateSize(), 100);
            
            return map;
            
        } catch (error) {
            console.error(`Error creating map for ${locationKey}:`, error);
            if (mapContainer) {
                mapContainer.innerHTML = `<p style="color: red; padding: 20px;">Error loading map: ${error.message}</p>`;
            }
            return null;
        }
    }

    // Find and initialize all map containers on the page
    function initializeAllMaps() {
        console.log('Searching for map containers on the page');
        
        // First, remove any standalone fallback messages
        const fallbackMsgs = document.querySelectorAll('#map-fallback');
        fallbackMsgs.forEach(msg => {
            msg.style.display = 'none';
        });
        
        // Map container ID to location key mapping
        const mapContainers = [
            { id: 'caboolture-church-map', location: 'caboolture' },
            { id: 'beachmere-church-map', location: 'beachmere' },
            { id: 'upper-caboolture-church-map', location: 'upperCaboolture' },
            { id: 'togan-congregation-map', location: 'toganCongregation' },
            { id: 'unitingcare-map', location: 'unitingcare' }
        ];
        
        // Initialize each map container found on the page
        let mapsInitialized = 0;
        
        mapContainers.forEach(container => {
            if (document.getElementById(container.id)) {
                initializeSpecificMap(container.id, container.location);
                mapsInitialized++;
            }
        });
        
        console.log(`Found and initialized ${mapsInitialized} maps`);
    }

    // Public API
    return {
        initialize: function() {
            loadLeafletResources(initializeAllMaps);
        },
        
        // Allow initializing a specific map if needed
        initializeMap: function(mapId, locationKey) {
            loadLeafletResources(() => {
                initializeSpecificMap(mapId, locationKey);
            });
        }
    };
})();
