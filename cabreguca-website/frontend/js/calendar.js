function initializeCalendar() {
    // Check if we're on the calendar page by looking for essential elements
    const monthYear = document.getElementById('month-year');
    const daysContainer = document.getElementById('days');

    // If essential elements don't exist, we're not on the calendar page
    if (!monthYear || !daysContainer) {
        console.log('Calendar elements not found, skipping initialization');
        return;
    }

    const prevButton = document.getElementById('prev');
    const nextButton = document.getElementById('next');
    const refreshButton = document.getElementById('refresh');
    const popupCloseButton = document.querySelector('.event-popup-close');
    const overlay = document.querySelector('.overlay');
    const specialEventsList = document.querySelector('.special-events-list');

    // Current date is the date that the user is currently viewing
    let currentDate = new Date();
    // Current global date is the date that the user is currently on
    const today = new Date();

    let isRefreshing = false;
    const THROTTLE_TIME = 2000; // 2 seconds

    // Function to render the calendar
    function renderCalendar(date) {
        const month = date.getMonth(); // 0-11
        const year = date.getFullYear();

        // Display month and year
        const monthName = new Intl.DateTimeFormat('en-AU', {
            month: 'long',
            timeZone: 'Australia/Brisbane'
        }).format(date);
        monthYear.textContent = `${monthName} ${year}`;

        daysContainer.innerHTML = '';

        // First day of the month
        const firstDay = new Date(year, month, 1).getDay();

        // Number of days in the month
        const daysInMonth = new Date(year, month + 1, 0).getDate();

        // Previous month's days (optional: to fill the first week)
        for (let i = 0; i < firstDay; i++) {
            const emptyDiv = document.createElement('div');
            emptyDiv.classList.add('empty');
            daysContainer.appendChild(emptyDiv);
        }

        // Current month's days
        for (let day = 1; day <= daysInMonth; day++) {
            const dayDiv = document.createElement('div');
            dayDiv.classList.add('day');
            dayDiv.setAttribute('data-day', day);

            // Create a span for the day number
            const dayNumberSpan = document.createElement('span');
            dayNumberSpan.classList.add('day-number');
            dayNumberSpan.textContent = day;

            dayDiv.appendChild(dayNumberSpan);

            // Highlight today's date only if it's in the current view month and year
            if (
                day === today.getDate() &&
                month === today.getMonth() &&
                year === today.getFullYear()
            ) {
                dayDiv.classList.add('today'); // applies circular border to today's date (in css)
            }

            daysContainer.appendChild(dayDiv);
        }

        // Fetch and display events for the current month and year
        // Adjust month as backend expects months in 1-12 format,
        // however javascript months have 0-based indexing.
        fetchCalendarData(month + 1, year);
    }

    function displayEvents(events) {
        // Sort events by date
        events.sort((a, b) => new Date(a.date) - new Date(b.date));

        events.forEach(event => {
            // Add event indicators to calendar days
            const eventDate = new Date(event.date);
            const dayDiv = document.querySelector(`.day[data-day="${eventDate.getDate()}"]`);

            if (dayDiv) {
                const eventIndicator = document.createElement('span');
                eventIndicator.classList.add('event-indicator');

                // Add special-event class for non-recurring events
                if (!event.recurrencePattern) {
                    eventIndicator.classList.add('special-event');
                }

                dayDiv.appendChild(eventIndicator);

                if (!dayDiv.events) {
                    dayDiv.events = [];
                }
                dayDiv.events.push(event);

                dayDiv.addEventListener('click', () => showPopup(dayDiv.events));
            }
        });
    }

    function showPopup(events) {
        const popup = document.querySelector('.event-popup');
        const eventList = popup.querySelector('.event-list');

        // Clear previous events
        eventList.innerHTML = '';

        events.forEach(event => {
            let recurrenceInfo = '';
            if (event.recurrencePattern) {
                recurrenceInfo = `
                    <p><strong>Recurrence Pattern:</strong> ${event.recurrencePattern}</p>
                `;
                if (event.recurrenceEndDate) {
                    const endDate = new Date(event.recurrenceEndDate);
                    recurrenceInfo += `<p><strong>Recurrence Ends:</strong> ${endDate.toLocaleDateString('en-AU')}</p>`;
                }
            }

            // Format the date with time
            const eventDate = new Date(event.date);
            const formattedDate = eventDate.toLocaleDateString('en-AU', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                timeZone: 'Australia/Brisbane'
            });

            // Format the time
            const formattedTime = eventDate.toLocaleTimeString('en-AU', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true,
                timeZone: 'Australia/Brisbane'
            });

            eventList.insertAdjacentHTML('beforeend', `
                <div class="event-item">
                    <h4>${event.title}</h4>
                    <p>${event.description}</p>
                    <p>Category: ${event.category}</p>
                    <p>Date: ${formattedDate}</p>
                    <p>Time: ${formattedTime}</p>
                    <p>Location: ${event.location}</p>
                    ${recurrenceInfo}
                </div>
            `);
        });

        popup.style.display = 'block';
        overlay.style.display = 'block';
    }

    function closePopup() {
        const popup = document.querySelector('.event-popup');
        const overlay = document.querySelector('.overlay');
        popup.style.display = 'none';
        overlay.style.display = 'none';
    }

    async function fetchCalendarData(month, year) {
        const loadingSpinner = document.getElementById('loading-spinner');
        try {
            // Show loading spinner
            loadingSpinner.style.display = 'block';

            // Fetch calendar data from the API endpoint
            const response = await fetch(`/api/Events?month=${month}&year=${year}`);
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            const calendarData = await response.json();

            // Process and display events on the calendar
            displayEvents(calendarData.eventInstances);

            // Display master events in the sidebar
            displayMasterEvents(calendarData.masterEvents);

            // Find and display special events (one-time events that don't recur)
            const specialEvents = calendarData.masterEvents.filter(event =>
                !event.recurrencePattern && new Date(event.date) >= today
            );
            displaySpecialEvents(specialEvents);

        } catch (error) {
            console.error('Error fetching calendar data:', error);
            alert('Failed to load calendar data. Please try again later.');
        } finally {
            // Hide loading spinner
            loadingSpinner.style.display = 'none';
        }
    }

    function displayMasterEvents(masterEvents) {
        const monthEventsList = document.querySelector('.month-events-list');
        monthEventsList.innerHTML = '';

        if (masterEvents.length === 0) {
            const noEventsMsg = document.createElement('p');
            noEventsMsg.textContent = 'No events found for this month.';
            monthEventsList.appendChild(noEventsMsg);
            return;
        }

        // Get the current date for "today"
        const today = new Date();
        // Set today to the beginning of the day
        today.setHours(0, 0, 0, 0);

        // Sort events by date (oldest to newest)
        masterEvents.sort((a, b) => new Date(a.date) - new Date(b.date));

        // Separate upcoming and past events
        const upcomingEvents = [];
        const pastEvents = [];

        masterEvents.forEach(event => {
            const eventDate = new Date(event.date);
            if (eventDate >= today) {
                upcomingEvents.push(event);
            } else {
                pastEvents.push(event);
            }
        });

        const appendEvents = (events) => {
            events.forEach(event => {
                const eventItem = document.createElement('div');
                eventItem.classList.add('month-event-item');

                // Add recurring class if the event has a recurrence pattern
                if (event.recurrencePattern) {
                    eventItem.classList.add('recurring');
                }

                const eventDate = new Date(event.date);

                const dateOptions = {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                };
                const formattedDate = eventDate.toLocaleDateString('en-AU', dateOptions);

                // Format time for display
                const formattedTime = eventDate.toLocaleTimeString('en-AU', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true,
                    timeZone: 'Australia/Brisbane'
                });

                // Format the event information with time
                let eventInfo = `
                    <div class="event-date-time">
                        <span class="event-date">${formattedDate}</span>
                        <span class="event-time">${formattedTime}</span>
                    </div>
                    <h5 class="event-title">${event.title}</h5>
                `;

                if (event.description) {
                    // Truncate description if it's too long
                    const maxLength = 100;
                    let description = event.description;
                    if (description.length > maxLength) {
                        description = description.substring(0, maxLength) + '...';
                    }
                    eventInfo += `<p class="event-description">${description}</p>`;
                }

                if (event.location) {
                    eventInfo += `<p class="event-location"><strong>Location:</strong> ${event.location}</p>`;
                }

                if (event.recurrencePattern) {
                    eventInfo += `
                        <p class="event-recurrence">
                            <span class="recurrence-icon">🔄</span> <strong>Recurring:</strong> ${event.recurrencePattern}
                        </p>
                    `;
                }

                eventItem.innerHTML = eventInfo;

                // Make the event clickable to show details
                eventItem.addEventListener('click', () => showPopup([event]));

                monthEventsList.appendChild(eventItem);
            });
        };

        // Append Upcoming Events if any
        if (upcomingEvents.length > 0) {
            const upcomingHeader = document.createElement('h4');
            upcomingHeader.textContent = 'Upcoming Events';
            upcomingHeader.classList.add('event-section-title');
            monthEventsList.appendChild(upcomingHeader);

            appendEvents(upcomingEvents);
        }

        // Append Past Events if any
        if (pastEvents.length > 0) {
            const pastHeader = document.createElement('h4');
            pastHeader.textContent = 'Past Events';
            pastHeader.classList.add('event-section-title');
            monthEventsList.appendChild(pastHeader);

            appendEvents(pastEvents);
        }
    }

    // display special events
    function displaySpecialEvents(specialEvents) {
        // Clear existing special events
        specialEventsList.innerHTML = '';

        if (!specialEvents || specialEvents.length === 0) {
            // If no special events, display a message
            const noEventsMsg = document.createElement('div');
            noEventsMsg.classList.add('no-special-events');
            noEventsMsg.textContent = 'No upcoming special events at this time.';
            specialEventsList.appendChild(noEventsMsg);
            return;
        }

        // Filter events for the current month and future events from other months
        const currentMonthStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const currentMonthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

        // Sort special events by date (closest first)
        specialEvents.sort((a, b) => new Date(a.date) - new Date(b.date));

        // Filter to only show upcoming events or events in the current month
        const eventsToShow = specialEvents.filter(event => {
            const eventDate = new Date(event.date);
            return (eventDate >= today) ||
                (eventDate >= currentMonthStart && eventDate <= currentMonthEnd);
        }).slice(0, 5); // Show top 5 events

        if (eventsToShow.length === 0) {
            const noEventsMsg = document.createElement('div');
            noEventsMsg.classList.add('no-special-events');
            noEventsMsg.textContent = 'No upcoming special events at this time.';
            specialEventsList.appendChild(noEventsMsg);
            return;
        }

        eventsToShow.forEach(event => {
            const eventDate = new Date(event.date);

            // Format time properly for display
            const formattedTime = eventDate.toLocaleTimeString('en-AU', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true,
                timeZone: 'Australia/Brisbane'
            });

            const specialEvent = document.createElement('div');
            specialEvent.classList.add('special-event');

            // Add highlighted class for non-recurring events to make them stand out
            if (!event.recurrencePattern) {
                specialEvent.classList.add('highlighted-event');
            }

            // Create date and event info elements with formatted time
            specialEvent.innerHTML = `
                <div class="event-date">
                    <div class="event-day">
                        <span class="day">${eventDate.getDate()}</span>
                        <span class="month">${eventDate.toLocaleDateString('en-AU', {
                            month: 'short',
                            timeZone: 'Australia/Brisbane'
                        })}</span>
                    </div>
                    <div class="event-info">
                        <h4 class="event-title">${event.title}</h4>
                        <p class="event-description">${event.description || 'No description available.'}</p>
                        <p class="event-time">${formattedTime}</p>
                        ${event.recurrencePattern ?
                            `<p class="event-recurrence"><span class="recurrence-icon">🔄</span> ${event.recurrencePattern}</p>` :
                            '<p class="event-special-tag">Special Event</p>'}
                    </div>
                </div>
            `;

            // Add click event to show full details
            specialEvent.addEventListener('click', () => {
                showPopup([{
                    date: eventDate,
                    title: event.title,
                    description: event.description,
                    location: event.location,
                    category: event.category,
                    recurrencePattern: event.recurrencePattern,
                    recurrenceEndDate: event.recurrenceEndDate
                }]);
            });

            specialEventsList.appendChild(specialEvent);
        });
    }

    // Helper function to safely get event date
    function eventDateOrNull(date) {
        return date ? date.getTime() : null;
    }

    /**
     * Helper function to capitalize the first letter of a string.
     * @param {string} string - The string to capitalize.
     * @returns {string} - The capitalized string.
     */
    function capitalizeFirstLetter(string) {
        if (!string) return '';
        return string.charAt(0).toUpperCase() + string.slice(1);
    }

    // Event listeners for navigation buttons
    if (prevButton) {
        prevButton.addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() - 1);
            renderCalendar(currentDate);
            fetchCalendarData(currentDate.getMonth() + 1, currentDate.getFullYear());
        });
    }

    if (nextButton) {
        nextButton.addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() + 1);
            renderCalendar(currentDate);
            fetchCalendarData(currentDate.getMonth() + 1, currentDate.getFullYear());
        });
    }

    if (refreshButton) {
        refreshButton.addEventListener('click', () => {
            if (refreshButton.disabled) return;

            currentDate = new Date();
            renderCalendar(currentDate);
            fetchCalendarData(currentDate.getMonth() + 1, currentDate.getFullYear());

            // Throttle the refresh button to prevent abuse
            refreshButton.disabled = true;
            refreshButton.classList.add('throttled');

            setTimeout(() => {
                refreshButton.disabled = false;
                refreshButton.classList.remove('throttled');
            }, THROTTLE_TIME);
        });
    }

    // Event listeners for closing the popup
    if (popupCloseButton) {
        popupCloseButton.addEventListener('click', closePopup);
    }
    if (overlay) {
        overlay.addEventListener('click', closePopup);
    }

    // Close popup when pressing "Escape" key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            closePopup();
        }
    });

    // Initial render
    renderCalendar(currentDate);
    fetchCalendarData(currentDate.getMonth() + 1, currentDate.getFullYear());
}

function getNextOccurrenceDate(event, fromDate) {
    const recurrencePattern = event.recurrencePattern ? event.recurrencePattern.toLowerCase() : null;
    const recurrenceEndDate = event.recurrenceEndDate ? new Date(event.recurrenceEndDate) : null;
    let nextDate = null;

    const eventDate = new Date(event.date);
    eventDate.setHours(0, 0, 0, 0);

    if (!recurrencePattern) {
        // Non-recurring event
        nextDate = eventDate >= fromDate ? eventDate : null;
    } else {
        // Recurring event
        nextDate = calculateNextRecurrence(eventDate, recurrencePattern, recurrenceEndDate, fromDate);
    }

    return nextDate;
}

function calculateNextRecurrence(eventDate, recurrencePattern, recurrenceEndDate, fromDate) {
    let nextDate = new Date(eventDate);
    nextDate.setHours(0, 0, 0, 0);

    if (recurrenceEndDate && fromDate > recurrenceEndDate) {
        return null; // Event recurrence has ended
    }

    while (nextDate < fromDate) {
        switch (recurrencePattern) {
            case 'daily':
                nextDate.setDate(nextDate.getDate() + 1);
                break;
            case 'weekly':
                nextDate.setDate(nextDate.getDate() + 7);
                break;
            case 'fortnightly':
                nextDate.setDate(nextDate.getDate() + 14);
                break;
            case 'monthly':
                nextDate.setMonth(nextDate.getMonth() + 1);
                break;
            default:
                // Unsupported recurrence pattern
                return null;
        }

        // Check recurrence end date
        if (recurrenceEndDate && nextDate > recurrenceEndDate) {
            return null; // No more occurrences
        }
    }

    return nextDate;
}

// Initialize the calendar when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCalendar();
});

// Export the initialize function
window.initializeCalendar = initializeCalendar;
