document.addEventListener('DOMContentLoaded', () => {
    const mainContent = document.getElementById('main-content');
    let initialPageLoaded = false; // Flag to detect the initial page load
    let currentPageLoaded = ''; // Track the currently loaded page

    // Function to hide the loader after initial content is loaded
    function hideLoader() {
        const loader = document.getElementById('loader');
        if (loader) {
            loader.style.opacity = '0';
            setTimeout(() => {
                loader.style.display = 'none';
            }, 500);
        }
    }

    // Function to scroll to a hash element
    function scrollToHash(hash) {
        if (hash) {
            const anchorElem = document.getElementById(hash.substring(1));
            if (anchorElem) {
                anchorElem.scrollIntoView({ behavior: 'smooth' });
            }
        }
    }

    // Modified loadPage to support hash fragments
    function loadPage(page, updateHistory = true, hash = "") {
        return new Promise((resolve, reject) => {
            // Show loader
            document.getElementById('loader').style.display = 'flex';
            
            // Fetch the page content
            fetch(`${page}.html`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    // Update content
                    mainContent.innerHTML = html;
                    
                    // Update history if needed, including the hash if present
                    if (updateHistory) {
                        const url = hash ? `/${page}${hash}` : `/${page}`;
                        history.pushState({ page, hash }, null, url);
                    }
                    
                    // Update current page
                    currentPageLoaded = page;
                    
                    // Hide loader
                    document.getElementById('loader').style.display = 'none';
                    
                    // Initialize calendar if needed
                    if (page === 'calendar') {
                        initializeCalendar();
                    }
                    
                    // Initialize map if on contact or care page
                    if (page === 'contact' || page === 'care') {
                        // Use the map module to initialize the map
                        if (typeof MapModule !== 'undefined') {
                            MapModule.initialize();
                        } else {
                            console.error('Map module not loaded');
                        }
                    }
                    
                    // Resolve the promise when done
                    resolve();
                })
                .catch(error => {
                    console.error(`Failed to load page: ${page}`, error);
                    // Fallback to home page if loading fails
                    if (page !== 'home') {
                        loadPage('home', updateHistory);
                    }
                    reject(error);
                });
        });
    }

    // Updated event delegation for routing links
    document.addEventListener(
        'click',
        (e) => {
            const link = e.target.closest('a[data-page]');
            if (link) {
                e.preventDefault();
                
                // Scroll to top immediately when link is clicked
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
                
                const page = link.getAttribute('data-page');
                const href = link.getAttribute('href') || "";
                let targetHash = "";

                // If href starts with '#', it's an in-page anchor
                if (href.startsWith('#') && href !== '#') {
                    targetHash = href;
                }

                if (currentPageLoaded === page) {
                    // Already on the desired page, update the hash and scroll
                    history.replaceState({ page, hash: targetHash }, null, `/${page}${targetHash}`);
                    if (targetHash) {
                        // Only scroll to hash if there is one, otherwise stay at top
                        setTimeout(() => {
                            scrollToHash(targetHash);
                        }, 300);
                    }
                } else {
                    // Load the new page with the hash included in the URL
                    loadPage(page, true, targetHash).then(() => {
                        // After the page is loaded, scroll to the hash if specified
                        if (targetHash) {
                            setTimeout(() => {
                                scrollToHash(targetHash);
                            }, 300);
                        }
                    });
                }
            }
        },
        true
    );

    // Handle scroll on hashchange
    window.addEventListener('hashchange', () => {
        scrollToHash(window.location.hash);
    });

    // Also update the popstate handler to handle hash fragments
    window.addEventListener('popstate', (event) => {
        if (event.state && event.state.page) {
            const page = event.state.page;
            const hash = event.state.hash || "";
            
            loadPage(page, false, hash).then(() => {
                if (hash) {
                    setTimeout(() => {
                        scrollToHash(hash);
                    }, 300);
                }
            });
        } else {
            loadPage('home', false);
        }
    });

    // Determine the initial page and load it
    const path = window.location.pathname.slice(1); // Remove the leading '/'
    const initialPage = path ? path : 'home';
    loadPage(initialPage, false);

    // --- Scroll to Top Button Functionality ---

    // Get the button
    const scrollToTopBtn = document.getElementById('scroll-to-top');

    // Show/hide the scroll-to-top button based on scroll position.
    window.addEventListener('scroll', () => {
        // On larger screens, show button only after scrolling down 100px.
        if (window.innerWidth > 767) {
            if (window.scrollY > 100) {
                scrollToTopBtn.style.display = 'block';
            } else {
                scrollToTopBtn.style.display = 'none';
            }
        } else {
            // On mobile devices, always display the button.
            scrollToTopBtn.style.display = 'block';
        }
    });

    // When the button is clicked, scroll to the top smoothly
    scrollToTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
});
