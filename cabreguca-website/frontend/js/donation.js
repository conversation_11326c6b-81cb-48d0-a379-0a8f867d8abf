// Stripe Elements
let stripe;
let elements;
let paymentElement;
let clientSecret;

// DOM Elements
const donationForm = document.getElementById('donation-form');
const submitButton = document.getElementById('submit-button');
const paymentMessage = document.getElementById('payment-message');
const donationSuccess = document.getElementById('donation-success');
const amountOptions = document.querySelectorAll('.amount-option');
const customAmountInput = document.querySelector('.custom-amount-input');
const customAmountButton = document.querySelector('.custom-amount');
const customAmount = document.getElementById('custom-amount');
const donationAmount = document.getElementById('donation-amount');
const anonymousDonation = document.getElementById('anonymous-donation');
const donorInfo = document.getElementById('donor-info');

// API URL - This can be injected by the server
const apiUrl = window.API_URL || '/api'; // Fallback to relative path if not injected

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    // Check if this is a redirect back from Stripe after a successful payment
    const urlParams = new URLSearchParams(window.location.search);
    const paymentIntentClientSecret = urlParams.get('payment_intent_client_secret');
    const paymentIntentId = urlParams.get('payment_intent');

    if (paymentIntentClientSecret && paymentIntentId) {
        // Payment was processed, check the status
        stripe = Stripe(window.STRIPE_PUBLISHABLE_KEY || 'pk_test_placeholder');
        stripe.retrievePaymentIntent(paymentIntentClientSecret).then(({paymentIntent}) => {
            if (paymentIntent.status === 'succeeded') {
                // Payment succeeded, show success message
                showSuccessMessage();
            } else {
                // Payment failed or is still processing
                showMessage(`Payment ${paymentIntent.status}: ${paymentIntent.last_payment_error?.message || 'An error occurred'}`);
            }
        });
    } else {
        // Normal page load
        initializeAmountButtons();
        initializeAnonymousToggle();
    }
});

// Initialize amount buttons
function initializeAmountButtons() {
    amountOptions.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons
            amountOptions.forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            button.classList.add('active');

            if (button.classList.contains('custom-amount')) {
                // Show custom amount input
                customAmountInput.style.display = 'block';
                customAmount.focus();
                donationAmount.value = '';
            } else {
                // Hide custom amount input and set predefined amount
                customAmountInput.style.display = 'none';
                const amount = button.getAttribute('data-amount');
                donationAmount.value = amount;
            }
        });
    });

    // Handle custom amount input
    customAmount.addEventListener('input', () => {
        const amount = parseFloat(customAmount.value);
        if (!isNaN(amount)) {
            // Convert to cents
            donationAmount.value = Math.round(amount * 100);
        } else {
            donationAmount.value = '';
        }
    });

    // Set default amount
    amountOptions[2].click(); // Select $50 by default
}

// Initialize anonymous donation toggle
function initializeAnonymousToggle() {
    anonymousDonation.addEventListener('change', () => {
        if (anonymousDonation.checked) {
            donorInfo.style.display = 'none';
        } else {
            donorInfo.style.display = 'block';
        }
    });
}

// Initialize Stripe
async function initializeStripe(amount) {
    try {
        // Create payment intent on the server
        const response = await fetch(`${apiUrl}/donations/create-payment-intent`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                amountInCents: parseInt(amount),
                currency: 'aud',
                purpose: document.getElementById('donation-purpose').value,
                isAnonymous: document.getElementById('anonymous-donation').checked,
                donorName: document.getElementById('donor-name').value,
                donorEmail: document.getElementById('donor-email').value,
                donorMessage: document.getElementById('donor-message').value
            }),
        });

        if (!response.ok) {
            throw new Error('Failed to create payment intent');
        }

        const data = await response.json();
        clientSecret = data.clientSecret;

        // Initialize Stripe
        stripe = Stripe(window.STRIPE_PUBLISHABLE_KEY || data.publishableKey);

        // Create payment element
        elements = stripe.elements({
            clientSecret,
            appearance: {
                theme: 'stripe',
                variables: {
                    colorPrimary: '#4F7942',
                    colorBackground: '#ffffff',
                    colorText: '#333333',
                    colorDanger: '#df1b41',
                    fontFamily: 'Roboto, Open Sans, Segoe UI, sans-serif',
                    spacingUnit: '4px',
                    borderRadius: '4px'
                }
            }
        });

        // Create and mount the Payment Element
        paymentElement = elements.create('payment');
        paymentElement.mount('#payment-element');

        return true;
    } catch (error) {
        showMessage(`Payment initialization error: ${error.message}`);
        return false;
    }
}

// Handle form submission
donationForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    const amount = donationAmount.value;

    if (!amount || amount <= 0) {
        showMessage('Please select or enter a valid donation amount.');
        return;
    }

    // Disable the submit button to prevent multiple clicks
    setLoading(true);

    // Initialize Stripe if not already initialized
    if (!stripe || !elements) {
        const initialized = await initializeStripe(amount);
        if (!initialized) {
            setLoading(false);
            return;
        }
    }

    // Confirm the payment
    const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
            // Handle success in the same page
            return_url: `${window.location.origin}/donate`,
        },
        redirect: 'if_required'
    });

    if (error) {
        if (error.type === 'card_error' || error.type === 'validation_error') {
            showMessage(error.message);
        } else {
            showMessage('An unexpected error occurred.');
        }
        setLoading(false);
    } else {
        // Payment succeeded
        showSuccessMessage();
    }
});

// Show a message to the user
function showMessage(messageText) {
    paymentMessage.textContent = messageText;
    paymentMessage.classList.remove('hidden');
    setTimeout(() => {
        paymentMessage.classList.add('hidden');
    }, 5000);
}

// Show success message
function showSuccessMessage() {
    donationForm.style.display = 'none';
    donationSuccess.classList.remove('hidden');
}

// Set loading state
function setLoading(isLoading) {
    if (isLoading) {
        submitButton.disabled = true;
        document.querySelector('#spinner').classList.remove('hidden');
        document.querySelector('#button-text').classList.add('hidden');
    } else {
        submitButton.disabled = false;
        document.querySelector('#spinner').classList.add('hidden');
        document.querySelector('#button-text').classList.remove('hidden');
    }
}
