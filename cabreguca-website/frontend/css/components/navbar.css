/* Navigation Bar - Desktop / Larger Screens */
.navbar {
    background-color: var(--primary-color);
    padding: 10px 20px;
    position: sticky;
    top: 0;
    width: 100%;
    z-index: 100;
    display: flex;
    justify-content: center;
}

/* Navigation List */
.navbar ul {
    list-style: none;
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin: 0;
    padding: 0;
}

/* Navigation Items */
.navbar li {
    margin: 0 15px;
}

/* Navigation Links */
.navbar a {
    color: var(--text-color);
    text-decoration: none;
    font-size: 1.1em;
    transition: color 0.3s;
}

/* Hover Effect */
.navbar a:hover {
    color: var(--primary-dark);
}

/* 
  On mobile devices, remove fixed positioning so the navbar scrolls normally.
  Adjust the breakpoint as needed (here using max-width: 768px).
*/
@media screen and (max-width: 768px) {
    .navbar {
        position: static;
        top: auto;
    }

    .navbar ul {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }

    .navbar li {
        margin: 10px;
        width: calc(50% - 20px); /* 2 items per row with margins */
        text-align: center;
    }
}
