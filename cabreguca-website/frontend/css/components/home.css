.img-container {
  display: flex;
  justify-content: center;
  position: relative;
  overflow: hidden;
  /* Apply a radial gradient mask */
  mask-image: radial-gradient(circle, rgba(0, 0, 0, 1) -50%, rgba(0, 0, 0, 0) 70%);
  -webkit-mask-image: radial-gradient(circle, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 90%);
  font-size: 0; /* Remove white space between inline elements */
}

.overlay-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

/* Pseudo-element for Gradient Overlay */
.overlay-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* Radial gradient: transparent in the center, opaque black at the edges */
  background: radial-gradient(circle, rgba(0, 0, 0, 0) -50%, rgba(0, 0, 0, 0) 110%);
  z-index: 1;
  pointer-events: none;
}

.overlay-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--primary-color);
  font-size: 3rem;
  font-weight: bold;
  text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9),
               2px 2px 4px rgba(0, 0, 0, 1);
  text-align: center;
  z-index: 10; /* Increased to make sure it's above everything */
  padding: 10px 20px;
  filter: none !important;
}

/* Unified container for images with hover effect */
.home-services-img {
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, filter 0.3s ease;
}

/* Hover effect for the container */
.home-services-img:hover {
  transform: scale(1.05);
  filter: brightness(1.5);
}

/* Base image styles */
.homepage-church-img {
  position: relative;
  z-index: 0;
  margin: 0;
  padding: 0;
  transition: none; /* Remove individual transitions */
}

/* Remove individual image hover effects */
.home-services-img:hover .homepage-church-img {
  transform: none;
  filter: none;
}

/* Apply blur to images only */
.home-services-img .homepage-church-img {
  /* filter: blur(1px); /* Adjust the value as needed - 2px is a subtle blur */
  transition: filter 0.3s ease; /* Keep the transition for the hover effect */
}

/* Reset on hover to ensure the images become sharp again */
.home-services-img:hover .homepage-church-img {
  filter: none; /* Remove blur on hover */
}

/* DESKTOP LAYOUT */
@media screen and (min-width: 768px) {
  .img-container {
    flex-direction: row;
    flex-wrap: nowrap;
  }
  
  .homepage-church-img {
    width: 40%; /* Wider to ensure more overlap */
    display: inline-block;
    vertical-align: middle;
  }
  
  /* Create overlap effect for desktop */
  .homepage-church-img:not(:first-child) {
    margin-left: -10%; /* Increased negative margin for more overlap */
  }
  
  /* Enhanced crossfade effect */
  .homepage-church-img {
    position: relative;
    mask-image: linear-gradient(to right, rgba(0,0,0,1) 70%, rgba(0,0,0,0) 100%);
    -webkit-mask-image: linear-gradient(to right, rgba(0,0,0,1) 70%, rgba(0,0,0,0) 100%);
  }
  
  /* First image fades on right side only */
  .homepage-church-img:first-child {
    mask-image: linear-gradient(to right, rgba(0,0,0,1) 70%, rgba(0,0,0,0) 100%);
    -webkit-mask-image: linear-gradient(to right, rgba(0,0,0,1) 70%, rgba(0,0,0,0) 100%);
  }
  
  /* Middle images fade on both sides */
  .homepage-church-img:not(:first-child):not(:last-child) {
    mask-image: linear-gradient(to right, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 30%, rgba(0,0,0,1) 70%, rgba(0,0,0,0) 100%);
    -webkit-mask-image: linear-gradient(to right, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 30%, rgba(0,0,0,1) 70%, rgba(0,0,0,0) 100%);
  }
  
  /* Last image fades on left side only */
  .homepage-church-img:last-child {
    mask-image: linear-gradient(to right, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 30%);
    -webkit-mask-image: linear-gradient(to right, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 30%);
  }
}

/* MOBILE LAYOUT */
@media screen and (max-width: 767px) {
  .img-container {
    flex-direction: column;
    align-items: center;
  }
  
  .homepage-church-img {
    width: 100%;
    display: block;
  }
  
  /* Create overlap effect for mobile */
  .homepage-church-img:not(:first-child) {
    margin-top: -20%; /* Increased negative margin for more overlap */
  }
  
  /* Enhanced vertical crossfade for mobile */
  .homepage-church-img {
    position: relative;
  }
  
  /* First image fades on bottom only */
  .homepage-church-img:first-child {
    mask-image: linear-gradient(to bottom, rgba(0,0,0,1) 70%, rgba(0,0,0,0) 100%);
    -webkit-mask-image: linear-gradient(to bottom, rgba(0,0,0,1) 70%, rgba(0,0,0,0) 100%);
  }
  
  /* Middle images fade on both top and bottom */
  .homepage-church-img:not(:first-child):not(:last-child) {
    mask-image: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 30%, rgba(0,0,0,1) 70%, rgba(0,0,0,0) 100%);
    -webkit-mask-image: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 30%, rgba(0,0,0,1) 70%, rgba(0,0,0,0) 100%);
  }
  
  /* Last image fades on top only */
  .homepage-church-img:last-child {
    mask-image: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 30%);
    -webkit-mask-image: linear-gradient(to bottom, rgba(0,0,0,0) 0%, rgba(0,0,0,1) 30%);
  }
}

/* Special case for the single image in online service */
.home-online-service-img .homepage-church-img {
  width: 100%;
  margin: 0;
  display: block;
  mask-image: none !important;
  -webkit-mask-image: none !important;
}