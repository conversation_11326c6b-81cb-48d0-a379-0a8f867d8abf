.home-header h1 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 3rem;
    font-size: 2rem;
    font-family: 'Trebuchet MS';
    font-style: italic;
}

.home-header h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 3rem;
    font-size: 1.5rem;
}
.home-title {
  text-align: center;
  color: var(--title-color);
  margin-bottom: 1rem;
  font-family: 'Pinyon Script', 'Tangerine', 'Petit Formal Script', 'Brush Script MT', cursive;
  font-size: 3.5rem;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  }

@media screen and (max-width: 768px) {
  .home-title {
    font-size: 2rem;
  } 
}
  
.home-title::after {
  content: '';
  width: 80%;
  height: 50px;
  background-image: url("/data/img/graphics/vintage_divider_titles.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% auto;
}

.home-description p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    color: var(--body-text-color);
    text-align: center;
}

.home-text-alt h2 {
    text-align: center;
    font-size: 1.5rem;
    color: var(--primary-color);
}
.home-text-alt {
    text-align: left;
    margin-top: 2rem;
    margin-left: 2rem;
    margin-right: 2rem;
    text-align: justify;
    text-justify: inter-word;
    hyphens: auto;
}

@media screen and (max-width: 768px) {
    .home-text-alt p {
        font-size: 0.8rem;
        line-height: 1.4;
        margin-bottom: 0.8rem;
    }
}

.home-text-subheading {
    text-align: center;
    font-size: 1.1rem;
    font-weight: 200;
    color: #808080;
    margin-top: -10px;
    font-style: italic;
    margin-bottom: 1rem;
}

.home-text-subheading-alt {
    font-weight: 600;
    color: #f1aa58;
    font-style: bold;
}

@media screen and (max-width: 768px) {
    .home-container {
        padding: 1rem;
    }

    .home-header h1 {
        font-size: 1.5rem;
    }

    .home-description p {
        font-size: 1rem;
    }
}

@media screen and (max-width: 480px) {
    .home-container {
        padding: 0.8rem;
    }

    .home-header h1 {
        font-size: 1.3rem;
    }

    .home-description p {
        font-size: 0.9rem;
    }
}

/* Button icon styling */
.button-icon {
  max-width: 75px;
  max-height: 75px;
  display: block;
  margin: 0 auto 3px auto;
  transition: transform 0.3s ease;
}

/* Add a subtle animation on button hover */
.button-link:hover .button-icon {
  transform: scale(1.05);
}

/* For smaller screens, adjust the icon size */
@media screen and (max-width: 768px) {
  .button-icon {
    max-width: 35px;
    max-height: 35px;
    margin-bottom: 5px;
  }
}
