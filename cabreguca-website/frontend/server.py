from http.server import HTTPServer, SimpleHTTPRequestHandler

class NoCacheHTTPRequestHandler(SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

if __name__ == '__main__':
    host = '0.0.0.0'
    port = 8000
    server_address = (host, port)
    httpd = HTTPServer(server_address, NoCacheHTTPRequestHandler)
    
    print(f"Server running on:")
    print(f" * Local:            http://localhost:{port}/")
    print(f" * Network:          http://{host}:{port}/")
    print("Press CTRL+C to quit")
    
    httpd.serve_forever()
