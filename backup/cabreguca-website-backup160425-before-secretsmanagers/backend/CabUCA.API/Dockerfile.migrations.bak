FROM mcr.microsoft.com/dotnet/sdk:8.0 AS migrations
WORKDIR /src

# Create non-root user
RUN adduser --disabled-password \
    --home /src \
    --gecos '' dotnetuser

# Create and set permissions for /Data directory
RUN mkdir -p /Data && chown -R dotnetuser:dotnetuser /Data

# Copy project file and restore dependencies
COPY ["CabUCA.API/CabUCA.API.csproj", "CabUCA.API/"]
COPY ["CabUCA.API/appsettings*.json", "./CabUCA.API/"]
RUN dotnet restore "CabUCA.API/CabUCA.API.csproj"

# Copy the remaining source code
COPY . .

# Change ownership to dotnetuser
RUN chown -R dotnetuser:dotnetuser /src

# Set the working directory to the project folder
WORKDIR "/src/CabUCA.API"

# Switch to non-root user
USER dotnetuser

# Install dotnet-ef tool globally for dotnetuser with matching version
RUN dotnet tool install --global dotnet-ef --version 8.0.12

# Update PATH to include the tool's location
ENV PATH="/src/.dotnet/tools:${PATH}"

# Verify dotnet-ef installation
RUN dotnet ef --version

# Entry point to run the migrations
ENTRYPOINT ["dotnet", "ef", "database", "update", "--verbose"]