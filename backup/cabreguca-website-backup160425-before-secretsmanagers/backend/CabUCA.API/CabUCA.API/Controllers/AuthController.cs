using CabUCA.API.Dtos.Auth;
using CabUCA.API.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace CabUCA.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly UserManager<User> _userManager;
        private readonly IConfiguration _configuration;

        public AuthController(UserManager<User> userManager, IConfiguration configuration)
        {
            _userManager = userManager;
            _configuration = configuration;
        }

        // POST: api/Auth/Register
        [HttpPost("Register")]
        public async Task<IActionResult> Register(RegisterDto registerDto)
        {
            var user = new User 
            { 
                UserName = registerDto.Username, 
                Email = registerDto.Email,
                LockoutEnabled = true // Enable lockout
            };
            var result = await _userManager.CreateAsync(user, registerDto.Password);

            if (!result.Succeeded)
                return BadRequest(result.Errors);

            // Assign the "User" role to new users
            await _userManager.AddToRoleAsync(user, "User");

            return Ok("User registered successfully!");
        }

        // POST: api/Auth/Login
        [HttpPost("Login")]
        public async Task<IActionResult> Login(LoginDto loginDto)
        {
            var user = await _userManager.FindByNameAsync(loginDto.Username);
            if (user == null)
            {
                Console.WriteLine("Login failed: User not found.");
                return Unauthorized(new { message = "Invalid credentials." });
            }

            if (await _userManager.IsLockedOutAsync(user))
            {
                var lockoutEnd = await _userManager.GetLockoutEndDateAsync(user);
                Console.WriteLine($"Login failed: User is locked out until {lockoutEnd}.");
                return Unauthorized(new { 
                    message = "Account is locked.", 
                    lockoutEnd = lockoutEnd 
                });
            }

            if (!await _userManager.CheckPasswordAsync(user, loginDto.Password))
            {
                await _userManager.AccessFailedAsync(user);
                var failedCount = await _userManager.GetAccessFailedCountAsync(user);
                var remainingAttempts = 8 - failedCount; // Ensure this matches Program.cs

                Console.WriteLine($"Login failed: Invalid password. Remaining attempts: {remainingAttempts}");

                if (remainingAttempts <= 0)
                {
                    Console.WriteLine("Account has been locked due to too many failed attempts.");
                    return Unauthorized(new { 
                        message = "Account has been locked due to too many failed attempts. Please try again in 15 minutes.",
                        lockoutEnd = await _userManager.GetLockoutEndDateAsync(user)
                    });
                }

                return Unauthorized(new { 
                    message = "Invalid credentials.", 
                    remainingAttempts = remainingAttempts 
                });
            }

            await _userManager.ResetAccessFailedCountAsync(user);
            var token = await GenerateJwtToken(user);
            Console.WriteLine("Login successful.");
            return Ok(new { Token = token });
        }

        // GET: api/Auth/user
        [HttpGet("User")]
        [Authorize]
        public async Task<IActionResult> GetCurrentUser()
        {
            var username = User.Identity?.Name;
            if (string.IsNullOrEmpty(username))
            {
                return BadRequest("Username claim not found in token");
            }

            var user = await _userManager.FindByNameAsync(username);
            if (user == null)
            {
                return NotFound();
            }

            return Ok(new { Username = user.UserName, Email = user.Email });
        }

        private async Task<string> GenerateJwtToken(User user)
        {
            var secret = Environment.GetEnvironmentVariable("JWT_SECRET");
            if (string.IsNullOrEmpty(secret))
            {
                throw new InvalidOperationException("JWT Secret environment variable (JWT_SECRET) is not configured.");
            }

            var key = Encoding.ASCII.GetBytes(secret);
            
            // Still use configuration for non-sensitive values
            var jwtSettings = _configuration.GetSection("JwtSettings");

            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, user.UserName),
                new Claim(ClaimTypes.NameIdentifier, user.Id)
            };

            // Retrieve user roles and add them as claims
            var roles = await _userManager.GetRolesAsync(user);
            foreach (var role in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddHours(2),
                Issuer = jwtSettings.GetValue<string>("Issuer"),
                Audience = jwtSettings.GetValue<string>("Audience"),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }
    }
}