using CabUCA.API.Dtos.Donations;
using CabUCA.API.Models;
using Stripe;

namespace CabUCA.API.Interfaces
{
    /// <summary>
    /// Interface for Stripe payment processing service
    /// </summary>
    public interface IStripeService
    {
        /// <summary>
        /// Creates a payment intent for a donation
        /// </summary>
        /// <param name="createDonationDto">The donation information</param>
        /// <returns>Payment intent information</returns>
        Task<PaymentIntentDto> CreatePaymentIntentAsync(CreateDonationDto createDonationDto);
        
        /// <summary>
        /// Processes a webhook event from Stripe
        /// </summary>
        /// <param name="json">The JSON payload from Stripe</param>
        /// <param name="signatureHeader">The Stripe signature header</param>
        /// <returns>True if the webhook was processed successfully</returns>
        Task<bool> ProcessWebhookAsync(string json, string signatureHeader);
        
        /// <summary>
        /// Updates a donation based on a payment intent
        /// </summary>
        /// <param name="paymentIntent">The payment intent from Stripe</param>
        /// <returns>The updated donation</returns>
        Task<Donation?> UpdateDonationFromPaymentIntentAsync(PaymentIntent paymentIntent);
    }
}
