using CabUCA.API.Models;

namespace CabUCA.API.Interfaces
{
    /// <summary>
    /// Interface for donation data operations
    /// </summary>
    public interface IDonationRepository
    {
        /// <summary>
        /// Gets all donations
        /// </summary>
        /// <returns>A collection of donations</returns>
        Task<IEnumerable<Donation>> GetAllDonationsAsync();
        
        /// <summary>
        /// Gets a donation by ID
        /// </summary>
        /// <param name="id">The donation ID</param>
        /// <returns>The donation if found, otherwise null</returns>
        Task<Donation?> GetDonationByIdAsync(int id);
        
        /// <summary>
        /// Gets a donation by payment intent ID
        /// </summary>
        /// <param name="paymentIntentId">The Stripe payment intent ID</param>
        /// <returns>The donation if found, otherwise null</returns>
        Task<Donation?> GetDonationByPaymentIntentIdAsync(string paymentIntentId);
        
        /// <summary>
        /// Creates a new donation
        /// </summary>
        /// <param name="donation">The donation to create</param>
        /// <returns>The created donation</returns>
        Task<Donation> CreateDonationAsync(Donation donation);
        
        /// <summary>
        /// Updates an existing donation
        /// </summary>
        /// <param name="donation">The donation to update</param>
        /// <returns>True if the donation was updated, otherwise false</returns>
        Task<bool> UpdateDonationAsync(Donation donation);
        
        /// <summary>
        /// Gets donations by user ID
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>A collection of donations made by the user</returns>
        Task<IEnumerable<Donation>> GetDonationsByUserIdAsync(string userId);
    }
}
