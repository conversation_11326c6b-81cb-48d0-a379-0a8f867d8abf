using System;
using System.Collections.Generic;
using System.Linq;
using CabUCA.API.Models;
using CabUCA.API.Dtos.Events;

namespace CabUCA.API.Services
{
    public class RecurrenceService
    {
        // Public method to generate recurring event instances
        public List<Event> GenerateRecurringEvents(Event originalEvent, int targetMonth, int targetYear)
        {
            var instances = new List<Event>();
            var recurrence = originalEvent.Recurrence;
            if (recurrence == null)
                return instances;

            DateTime startDate = originalEvent.Date;
            DateTime endDate = recurrence.RecurrenceEndDate ?? DateTime.Now.AddYears(1);

            // Handle monthly recurrence specially to preserve the weekday ordinal (e.g., third Wednesday)
            if (recurrence.RecurrencePattern.ToLower() == "monthly")
            {
                // Compute the original event's ordinal (nth occurrence of the weekday in its month)
                int ordinal = (startDate.Day - 1) / 7 + 1;
                DayOfWeek eventDayOfWeek = startDate.DayOfWeek;

                // Iterate through each month from the event's start month until the end date
                DateTime currentMonth = new DateTime(startDate.Year, startDate.Month, 1);
                while (currentMonth <= endDate)
                {
                    DateTime occurrence;
                    try
                    {
                        occurrence = GetNthWeekday(currentMonth.Year, currentMonth.Month, eventDayOfWeek, ordinal);
                    }
                    catch (InvalidOperationException)
                    {
                        // If the nth occurrence does not exist in this month, skip to the next month
                        currentMonth = currentMonth.AddMonths(1);
                        continue;
                    }

                    // Don't return occurrences before the original event start date
                    if (occurrence < startDate)
                    {
                        currentMonth = currentMonth.AddMonths(1);
                        continue;
                    }

                    if (occurrence > endDate)
                        break;

                    // Only add an event if the occurrence falls in the requested target month and year
                    if (occurrence.Month == targetMonth && occurrence.Year == targetYear)
                    {
                        if (recurrence.ExclusionDates != null &&
                            recurrence.ExclusionDates.Any(ex => ex.Date.Date == occurrence.Date))
                        {
                            // Skip excluded dates
                        }
                        else
                        {
                            var recurringEvent = new Event
                            {
                                Title = originalEvent.Title,
                                Date = occurrence,
                                Description = originalEvent.Description,
                                Category = originalEvent.Category,
                                Location = originalEvent.Location,
                                ImageURL = originalEvent.ImageURL,
                                URL = originalEvent.URL,
                                EnteredBy = originalEvent.EnteredBy,
                                EnteredDate = originalEvent.EnteredDate,
                                UpdatedBy = originalEvent.UpdatedBy,
                                UpdatedDate = originalEvent.UpdatedDate,
                                Visibility = originalEvent.Visibility,
                                ViewCount = originalEvent.ViewCount,
                                ParticipantCount = originalEvent.ParticipantCount
                            };
                            instances.Add(recurringEvent);
                        }
                    }
                    currentMonth = currentMonth.AddMonths(1);
                }
                return instances;
            }
            else
            {
                // For other recurrence patterns (daily, weekly, fortnightly), use the existing logic.
                Func<DateTime, DateTime> getNextDate = recurrence.RecurrencePattern.ToLower() switch
                {
                    "daily" => date => date.AddDays(1),
                    "weekly" => date => date.AddDays(7),
                    "fortnightly" => date => date.AddDays(14),
                    _ => date => date.AddDays(1)
                };

                DateTime current = startDate;
                while (current <= endDate)
                {
                    if (current.Month != targetMonth || current.Year != targetYear)
                    {
                        current = getNextDate(current);
                        continue;
                    }

                    if (recurrence.ExclusionDates != null &&
                        recurrence.ExclusionDates.Any(ex => ex.Date.Date == current.Date))
                    {
                        current = getNextDate(current);
                        continue;
                    }

                    var recurringEvent = new Event
                    {
                        Title = originalEvent.Title,
                        Date = current,
                        Description = originalEvent.Description,
                        Category = originalEvent.Category,
                        Location = originalEvent.Location,
                        ImageURL = originalEvent.ImageURL,
                        URL = originalEvent.URL,
                        EnteredBy = originalEvent.EnteredBy,
                        EnteredDate = originalEvent.EnteredDate,
                        UpdatedBy = originalEvent.UpdatedBy,
                        UpdatedDate = originalEvent.UpdatedDate,
                        Visibility = originalEvent.Visibility,
                        ViewCount = originalEvent.ViewCount,
                        ParticipantCount = originalEvent.ParticipantCount
                    };

                    instances.Add(recurringEvent);
                    current = getNextDate(current);
                }
                return instances;
            }
        }

        // Helper to check if a recurring event occurs on a weekly basis within the target month/year.
        public bool CheckWeeklyRecurrence(EventMasterDto evt, int targetMonth, int targetYear)
        {
            int eventWeekday = (int)evt.Date.DayOfWeek;
            DateTime firstOfMonth = new DateTime(targetYear, targetMonth, 1);
            int firstWeekday = (int)firstOfMonth.DayOfWeek;
            int daysOffset = (eventWeekday - firstWeekday + 7) % 7;
            DateTime firstOccurrence = firstOfMonth.AddDays(daysOffset);

            return firstOccurrence.Month == targetMonth &&
                   (!evt.RecurrenceEndDate.HasValue || firstOccurrence <= evt.RecurrenceEndDate.Value);
        }

        // Helper to check fortnightly recurrence: adapts the weekly check to a two-week interval.
        public bool CheckFortnightlyRecurrence(EventMasterDto evt, int targetMonth, int targetYear)
        {
            // Calculate the first occurrence using weekly logic.
            DateTime firstOccurrence = GetFirstOccurrence(evt.Date, targetMonth, targetYear);

            // If there is no valid first occurrence, return false.
            if (firstOccurrence == DateTime.MinValue)
                return false;

            // Determine the difference in days from the original event date to the first occurrence.
            int daysDiff = (firstOccurrence - evt.Date).Days;

            // If the difference in days is a multiple of 14, this occurrence is valid.
            return daysDiff % 14 == 0;
        }

        // Helper to check monthly recurrence based on the nth weekday (e.g., 3rd Saturday)
        public bool CheckMonthlyRecurrence(EventMasterDto evt, int targetMonth, int targetYear)
        {
            // Determine the week number of the event's original date within its month.
            int originalWeekNumber = (evt.Date.Day - 1) / 7 + 1;
            // Find the target month's nth occurrence of the same weekday.
            DateTime firstOfMonth = new DateTime(targetYear, targetMonth, 1);
            int firstWeekday = (int)firstOfMonth.DayOfWeek;
            int eventWeekday = (int)evt.Date.DayOfWeek;
            int daysOffset = (eventWeekday - firstWeekday + 7) % 7;
            DateTime nthWeekday = firstOfMonth.AddDays(daysOffset + (originalWeekNumber - 1) * 7);

            // Confirm that nthWeekday falls within the target month and is not past the recurrence end date (if any)
            return nthWeekday.Month == targetMonth && 
                   (!evt.RecurrenceEndDate.HasValue || nthWeekday <= evt.RecurrenceEndDate.Value);
        }

        // Private helper to calculate the first occurrence in a month; returns DateTime.MinValue if not found.
        private DateTime GetFirstOccurrence(DateTime eventDate, int targetMonth, int targetYear)
        {
            int eventWeekday = (int)eventDate.DayOfWeek;
            DateTime firstOfMonth = new DateTime(targetYear, targetMonth, 1);
            int firstWeekday = (int)firstOfMonth.DayOfWeek;
            int daysOffset = (eventWeekday - firstWeekday + 7) % 7;
            DateTime firstOccurrence = firstOfMonth.AddDays(daysOffset);
            return firstOccurrence.Month == targetMonth ? firstOccurrence : DateTime.MinValue;
        }

        // Helper method to compute the nth occurrence of a weekday in a given month.
        private DateTime GetNthWeekday(int year, int month, DayOfWeek dayOfWeek, int n)
        {
            DateTime firstOfMonth = new DateTime(year, month, 1);
            int daysOffset = ((int)dayOfWeek - (int)firstOfMonth.DayOfWeek + 7) % 7;
            DateTime nthWeekday = firstOfMonth.AddDays(daysOffset + (n - 1) * 7);
            if (nthWeekday.Month != month)
            {
                throw new InvalidOperationException("The nth occurrence does not exist for this month.");
            }
            return nthWeekday;
        }
    }
} 