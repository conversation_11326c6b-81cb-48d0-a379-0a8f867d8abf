using System.ComponentModel.DataAnnotations;

namespace CabUCA.API.Dtos.Events;

public class CreateEventDto
{
    [Required]
    public string Title { get; set; }
    
    [Required]
    public DateTime Date { get; set; }
    
    public string Description { get; set; }
    
    public string Category { get; set; }
    public int LikeCount { get; set; }
    public string ImageURL { get; set; }
    public string URL { get; set; }
    // Admin details
    // handled by auth token:
    // public string EnteredBy { get; set; }
    public DateTime EnteredDate { get; set; }
    public DateTime UpdatedDate { get; set; }
    public int Visibility { get; set; }
    public int ViewCount { get; set; }
    public int ParticipantCount { get; set; }
    public string Location { get; set; }

    // Recurrence details
    public string? RecurrencePattern { get; set; } // e.g., "Daily", "Weekly", "Monthly"
    public DateTime? RecurrenceEndDate { get; set; }

    // Exclusion dates
    public List<DateTime>? ExclusionDates { get; set; }
}