namespace CabUCA.API.Dtos.Events;

public class EventDto
{
    public int Id { get; set; }
    
    public string Title { get; set; }
    
    public DateTime Date { get; set; }
    
    public string Description { get; set; }
    
    public string Category { get; set; }
    public int LikeCount { get; set; }
    public string ImageURL { get; set; }
    public string URL { get; set; }
    public string EnteredBy { get; set; }
    public DateTime EnteredDate { get; set; }
    public string UpdatedBy { get; set; }
    public DateTime UpdatedDate { get; set; }
    public int Visibility { get; set; }
    public int ViewCount { get; set; }
    public int ParticipantCount { get; set; }
    public string Location { get; set; }

    // Recurrence details
    public string? RecurrencePattern { get; set; }
    public DateTime? RecurrenceEndDate { get; set; }
    public List<DateTime>? ExclusionDates { get; set; }
}
