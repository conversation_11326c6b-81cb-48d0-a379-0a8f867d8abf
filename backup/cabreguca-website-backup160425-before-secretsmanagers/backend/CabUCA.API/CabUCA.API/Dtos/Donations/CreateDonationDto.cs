using System.ComponentModel.DataAnnotations;

namespace CabUCA.API.Dtos.Donations
{
    /// <summary>
    /// Data transfer object for creating a new donation
    /// </summary>
    public class CreateDonationDto
    {
        /// <summary>
        /// The amount of the donation in cents
        /// </summary>
        [Required]
        [Range(1, long.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public long AmountInCents { get; set; }
        
        /// <summary>
        /// The currency of the donation (e.g., AUD)
        /// </summary>
        [Required]
        [StringLength(3)]
        public string Currency { get; set; } = "AUD";
        
        /// <summary>
        /// The purpose or designation of the donation
        /// </summary>
        [StringLength(200)]
        public string? Purpose { get; set; }
        
        /// <summary>
        /// Whether the donation is anonymous
        /// </summary>
        public bool IsAnonymous { get; set; }
        
        /// <summary>
        /// The name of the donor (null if anonymous)
        /// </summary>
        [StringLength(100)]
        public string? DonorName { get; set; }
        
        /// <summary>
        /// The email of the donor (null if anonymous)
        /// </summary>
        [StringLength(100)]
        [EmailAddress]
        public string? DonorEmail { get; set; }
        
        /// <summary>
        /// Optional message from the donor
        /// </summary>
        [StringLength(500)]
        public string? DonorMessage { get; set; }
    }
}
