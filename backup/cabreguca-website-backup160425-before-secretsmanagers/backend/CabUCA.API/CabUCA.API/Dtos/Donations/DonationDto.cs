namespace CabUCA.API.Dtos.Donations
{
    /// <summary>
    /// Data transfer object for donation information
    /// </summary>
    public class DonationDto
    {
        /// <summary>
        /// The unique identifier for the donation
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// The amount of the donation in cents
        /// </summary>
        public long AmountInCents { get; set; }
        
        /// <summary>
        /// The currency of the donation (e.g., AUD)
        /// </summary>
        public string Currency { get; set; } = string.Empty;
        
        /// <summary>
        /// The date and time when the donation was made
        /// </summary>
        public DateTime DonationDate { get; set; }
        
        /// <summary>
        /// The purpose or designation of the donation
        /// </summary>
        public string? Purpose { get; set; }
        
        /// <summary>
        /// Whether the donation is anonymous
        /// </summary>
        public bool IsAnonymous { get; set; }
        
        /// <summary>
        /// The name of the donor (null if anonymous)
        /// </summary>
        public string? DonorName { get; set; }
        
        /// <summary>
        /// The email of the donor (null if anonymous)
        /// </summary>
        public string? DonorEmail { get; set; }
        
        /// <summary>
        /// Optional message from the donor
        /// </summary>
        public string? DonorMessage { get; set; }
        
        /// <summary>
        /// The status of the payment (succeeded, failed, processing)
        /// </summary>
        public string PaymentStatus { get; set; } = string.Empty;
    }
}
