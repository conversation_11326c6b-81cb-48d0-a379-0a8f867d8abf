namespace CabUCA.API.Config
{
    /// <summary>
    /// Configuration settings for Stripe integration
    /// </summary>
    public class StripeSettings
    {
        /// <summary>
        /// The Stripe API secret key
        /// </summary>
        public string SecretKey { get; set; } = string.Empty;
        
        /// <summary>
        /// The Stripe API publishable key
        /// </summary>
        public string PublishableKey { get; set; } = string.Empty;
        
        /// <summary>
        /// The webhook secret for validating Stripe webhook events
        /// </summary>
        public string WebhookSecret { get; set; } = string.Empty;
    }
}
