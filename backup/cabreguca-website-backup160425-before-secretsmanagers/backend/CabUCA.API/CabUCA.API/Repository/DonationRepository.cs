using CabUCA.API.Data;
using CabUCA.API.Interfaces;
using CabUCA.API.Models;
using Microsoft.EntityFrameworkCore;

namespace CabUCA.API.Repository
{
    /// <summary>
    /// Repository for donation data operations
    /// </summary>
    public class DonationRepository : IDonationRepository
    {
        private readonly AppDbContext _dbContext;
        private readonly ILogger<DonationRepository> _logger;

        /// <summary>
        /// Initializes a new instance of the DonationRepository class
        /// </summary>
        public DonationRepository(AppDbContext dbContext, ILogger<DonationRepository> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// Gets all donations
        /// </summary>
        public async Task<IEnumerable<Donation>> GetAllDonationsAsync()
        {
            try
            {
                return await _dbContext.Donations
                    .OrderByDescending(d => d.DonationDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all donations");
                return Enumerable.Empty<Donation>();
            }
        }

        /// <summary>
        /// Gets a donation by ID
        /// </summary>
        public async Task<Donation?> GetDonationByIdAsync(int id)
        {
            try
            {
                return await _dbContext.Donations
                    .FirstOrDefaultAsync(d => d.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving donation with ID: {id}");
                return null;
            }
        }

        /// <summary>
        /// Gets a donation by payment intent ID
        /// </summary>
        public async Task<Donation?> GetDonationByPaymentIntentIdAsync(string paymentIntentId)
        {
            try
            {
                return await _dbContext.Donations
                    .FirstOrDefaultAsync(d => d.PaymentIntentId == paymentIntentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving donation with payment intent ID: {paymentIntentId}");
                return null;
            }
        }

        /// <summary>
        /// Creates a new donation
        /// </summary>
        public async Task<Donation> CreateDonationAsync(Donation donation)
        {
            try
            {
                _dbContext.Donations.Add(donation);
                await _dbContext.SaveChangesAsync();
                return donation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating donation");
                throw;
            }
        }

        /// <summary>
        /// Updates an existing donation
        /// </summary>
        public async Task<bool> UpdateDonationAsync(Donation donation)
        {
            try
            {
                _dbContext.Donations.Update(donation);
                await _dbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating donation with ID: {donation.Id}");
                return false;
            }
        }

        /// <summary>
        /// Gets donations by user ID
        /// </summary>
        public async Task<IEnumerable<Donation>> GetDonationsByUserIdAsync(string userId)
        {
            try
            {
                return await _dbContext.Donations
                    .Where(d => d.UserId == userId)
                    .OrderByDescending(d => d.DonationDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving donations for user ID: {userId}");
                return Enumerable.Empty<Donation>();
            }
        }
    }
}
