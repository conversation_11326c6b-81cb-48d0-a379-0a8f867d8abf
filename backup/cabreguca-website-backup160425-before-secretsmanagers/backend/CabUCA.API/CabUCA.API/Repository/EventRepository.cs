using CabUCA.API.Data;
using CabUCA.API.Interfaces;
using CabUCA.API.Models;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace CabUCA.API.Repository;

public class EventRepository : IEventRepository
{
    private readonly AppDbContext _context;

    public EventRepository(AppDbContext context)
    {
        _context = context;
    }
    
    
    public async Task<List<Event>> GetAllEventsAsync()
    { 
        return await _context.Events.ToListAsync();
    }

    public async Task<Event?> GetEventAsync(int id)
    {
        return await _context.Events.FindAsync(id);
    }

    public async Task<Event> CreateEventAsync(Event @event)
    {
        // Set default values if not provided
        if (@event.EnteredDate == default)
            @event.EnteredDate = DateTime.Now;
        if (@event.UpdatedDate == default)
            @event.UpdatedDate = DateTime.Now;
        if (@event.LikeCount == default)
            @event.LikeCount = 0;
        if(@event.ImageURL == default)
            @event.ImageURL = "";
        if(@event.URL == default)
            @event.URL = "/events/" + @event.Id;
        if(@event.EnteredBy == default)
            @event.EnteredBy = "Unknown";
        if(@event.UpdatedBy == default)
            @event.UpdatedBy = "Unknown";
        if(@event.Visibility == default)
            @event.Visibility = 1;
        if(@event.ViewCount == default)
            @event.ViewCount = 0;
        if(@event.ParticipantCount == default)
            @event.ParticipantCount = 0;
        if(@event.Location == default)
            @event.Location = "Send enquiry to the admin for location";
        
        await _context.Events.AddAsync(@event);
        await _context.SaveChangesAsync();
        return @event;
    }

    public async Task<Event?> UpdateEventAsync(Event updatedEvent)
    {
        // Attach the entity if not already tracked
        if (!_context.Events.Local.Any(e => e.Id == updatedEvent.Id))
        {
            _context.Events.Attach(updatedEvent);
        }

        

        _context.Entry(updatedEvent).State = EntityState.Modified;

        try
        {
            await _context.SaveChangesAsync();
            return updatedEvent;
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!_context.Events.Any(e => e.Id == updatedEvent.Id))
            {
                return null;
            }
            else
            {
                throw;
            }
        }
    }

    public async Task<Event?> DeleteEventAsync(int id)
    {
        var @event = await _context.Events.FirstOrDefaultAsync(x => x.Id == id);
        if (@event == null)
        {
            return null;
        }

        _context.Events.Remove(@event);
        await _context.SaveChangesAsync();
        return @event;
    }

    public bool EventExists(int id)
    {
        return _context.Events.Any(e => e.Id == id);
    }
}