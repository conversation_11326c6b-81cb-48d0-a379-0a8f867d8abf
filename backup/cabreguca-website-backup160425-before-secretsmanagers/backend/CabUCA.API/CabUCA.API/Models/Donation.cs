using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CabUCA.API.Models
{
    /// <summary>
    /// Represents a donation made to the church
    /// </summary>
    public class Donation
    {
        [Key]
        public int Id { get; set; }
        
        /// <summary>
        /// The amount of the donation in cents
        /// </summary>
        public long AmountInCents { get; set; }
        
        /// <summary>
        /// The currency of the donation (e.g., AUD)
        /// </summary>
        [Required]
        [StringLength(3)]
        public string Currency { get; set; } = "AUD";
        
        /// <summary>
        /// The date and time when the donation was made
        /// </summary>
        public DateTime DonationDate { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// The purpose or designation of the donation
        /// </summary>
        [StringLength(200)]
        public string? Purpose { get; set; }
        
        /// <summary>
        /// Whether the donation is anonymous
        /// </summary>
        public bool IsAnonymous { get; set; }
        
        /// <summary>
        /// The name of the donor (null if anonymous)
        /// </summary>
        [StringLength(100)]
        public string? DonorName { get; set; }
        
        /// <summary>
        /// The email of the donor (null if anonymous)
        /// </summary>
        [StringLength(100)]
        public string? DonorEmail { get; set; }
        
        /// <summary>
        /// Optional message from the donor
        /// </summary>
        [StringLength(500)]
        public string? DonorMessage { get; set; }
        
        /// <summary>
        /// The Stripe payment intent ID associated with this donation
        /// </summary>
        [StringLength(100)]
        public string? PaymentIntentId { get; set; }
        
        /// <summary>
        /// The status of the payment (succeeded, failed, processing)
        /// </summary>
        [StringLength(20)]
        public string PaymentStatus { get; set; } = "pending";
        
        /// <summary>
        /// Optional user ID if the donor is a registered user
        /// </summary>
        public string? UserId { get; set; }
        
        /// <summary>
        /// Navigation property for the user if the donor is a registered user
        /// </summary>
        [ForeignKey("UserId")]
        public User? User { get; set; }
    }
}
