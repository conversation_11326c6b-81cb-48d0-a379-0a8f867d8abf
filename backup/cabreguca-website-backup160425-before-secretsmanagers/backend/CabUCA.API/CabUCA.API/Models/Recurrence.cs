using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using System.ComponentModel.DataAnnotations;

namespace CabUCA.API.Models
{
    public class Recurrence
    {
        public int Id { get; set; }

        // Foreign key to Event
        public int EventId { get; set; }

        // Navigation property to Event
        [JsonIgnore]
        public Event Event { get; set; }

        [Required]
        public string RecurrencePattern { get; set; } // e.g., "Daily", "Weekly", "Fortnightly", "Monthly"

        public DateTime? RecurrenceEndDate { get; set; }

        // Navigation property to ExclusionDates
        public List<ExclusionDate> ExclusionDates { get; set; } = new List<ExclusionDate>();
    }
}