using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace CabUCA.API.Models;

public class Event
{
    [Key] // EF will generate the ID, explicitly set the key attribute to avoid errors
    public int Id { get; set; }
    
    [Required]
    public string Title { get; set; } = string.Empty;
    
    [Required]
    public DateTime Date { get; set; }
    
    [Required]
    public string? Description { get; set; } = string.Empty;
    
    public string? Category { get; set; } = string.Empty;
    public int LikeCount { get; set; }
    public string? ImageURL { get; set; } = string.Empty;
    public string? URL { get; set; } = string.Empty;
    // Admin details
    [Required]
    public string EnteredBy { get; set; } = string.Empty;
    public DateTime EnteredDate { get; set; }
    [Required]
    public string UpdatedBy { get; set; } = string.Empty;
    public DateTime UpdatedDate { get; set; }
    public int Visibility { get; set; }
    public int ViewCount { get; set; }
    public int ParticipantCount { get; set; }
    public string? Location { get; set; } = string.Empty;

    // Navigation property to Recurrence
    public Recurrence? Recurrence { get; set; }
}