using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace CabUCA.API.Models
{
    public class ExclusionDate
    {
        public int Id { get; set; }

        [Required]
        public DateTime Date { get; set; }

        // Foreign key to Recurrence
        public int RecurrenceId { get; set; }

        // Navigation property to Recurrence
        [JsonIgnore]
        public Recurrence Recurrence { get; set; }
    }
} 