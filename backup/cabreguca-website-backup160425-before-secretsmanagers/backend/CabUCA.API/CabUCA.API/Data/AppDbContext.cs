// backend/CabUCA.API/Data/AppDbContext.cs

using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using CabUCA.API.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;

namespace CabUCA.API.Data
{
   public class AppDbContext : IdentityDbContext<User>
   {
       public AppDbContext(DbContextOptions<AppDbContext> options)
           : base(options)
       // TODO use DbContextOptionsBuilder<AppDbContext> to create instances of this class
       // (maybe using OnModelCreating() param)
       {
       }

       public DbSet<Event> Events { get; set; }
       public DbSet<Recurrence> Recurrences { get; set; }
       public DbSet<ExclusionDate> ExclusionDates { get; set; }
       public DbSet<Donation> Donations { get; set; }

       protected override void OnModelCreating(ModelBuilder modelBuilder)
       {
           base.OnModelCreating(modelBuilder);

           // Configure Recurrence - Event relationship
           modelBuilder.Entity<Recurrence>()
               .HasOne(r => r.Event)
               .WithOne(e => e.Recurrence)
               .HasForeignKey<Recurrence>(r => r.EventId)
               .OnDelete(DeleteBehavior.Cascade);

           // Configure ExclusionDate - Recurrence relationship
           modelBuilder.Entity<ExclusionDate>()
               .HasOne(ed => ed.Recurrence)
               .WithMany(r => r.ExclusionDates)
               .HasForeignKey(ed => ed.RecurrenceId)
               .OnDelete(DeleteBehavior.Cascade);

           // Configure the Event entity if needed
           modelBuilder.Entity<Event>()
               .HasKey(e => e.Id); // Ensure primary key is set
       }
   }
}