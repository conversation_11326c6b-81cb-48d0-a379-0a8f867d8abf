#!/bin/bash

# This script is the entrypoint for the CabUCA.API container.
# It will wait for the database to be ready, and then start the application.
# it will show a message to the console when the application is ready to use.

set -e

# Wait for database to be ready (if needed) 
echo "Waiting 3 seconds for database to be ready..."
sleep 3

cd /app

# Check if the DLL exists, if not, output error and build the project
if [ ! -f "CabUCA.API.dll" ]; then
    echo "Error: CabUCA.API.dll not found in $(pwd)"
    ls -la
    exit 1
fi

# Start the application
echo "Starting the application..."
exec dotnet CabUCA.API.dll
echo "Application successfully started at $(date)"
