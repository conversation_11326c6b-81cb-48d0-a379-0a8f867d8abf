#!/bin/bash
# This script loads the environment variables from the .env file into the current shell
# it will export the variables with the explicit export command, which is sometimes required
# to run in all shells.

while IFS='=' read -r key value; do
    # Skip comments and empty lines
    [[ $key =~ ^#.*$ ]] && continue
    [[ -z $key ]] && continue
    # Remove any leading/trailing whitespace and quotes
    key=$(echo $key | tr -d ' ')
    value=$(echo $value | tr -d '"' | tr -d "'")
    # Export the variable with explicit export command
    export "${key}=${value}"
    # Verify the export - only show last 4 chars of value for security
    masked_value="${value: -4}"
    echo "Exported: ${key}=***${masked_value}"
done < .env
