{"name": "admin-frontend", "version": "0.1.0", "private": true, "dependencies": {"@svgr/plugin-svgo": "^6.0.0", "@svgr/webpack": "^6.0.0", "axios": "^1.7.9", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "cra-template": "1.2.0", "react": "^19.0.0", "react-bootstrap": "^2.10.8", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-router-dom": "^7.1.3", "react-scripts": "^5.0.1", "resolve-url-loader": "^5.0.0", "svgo": "^2.8.0", "web-vitals": "^4.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "preinstall": "npx npm-force-resolutions"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"svgo": "^2.8.0", "@svgr/plugin-svgo": "^6.0.0", "@svgr/webpack": "^6.0.0", "nth-check": "^2.0.1", "postcss": "^8.4.31"}, "devDependencies": {"npm-force-resolutions": "^0.0.10"}}