# Stage 1: Build Stage
FROM node:14-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files and install dependencies
COPY package.json package-lock.json ./
RUN npm install

# Copy the rest of the source code
COPY . .

# Set environment variables
# ARG REACT_APP_API_DOMAIN_NAME
# ARG REACT_APP_DOMAIN_NAME
# pass it to the build to initialise variables in the react build
ENV REACT_APP_API_DOMAIN_NAME=${REACT_APP_API_DOMAIN_NAME}
ENV REACT_APP_DOMAIN_NAME=${REACT_APP_DOMAIN_NAME}

RUN echo "Building with API Domain: ${REACT_APP_API_DOMAIN_NAME} and Domain: ${REACT_APP_DOMAIN_NAME}"
RUN echo "Building with API Domain: ${REACT_APP_API_DOMAIN_NAME} and Domain: ${REACT_APP_DOMAIN_NAME}"

# Build the application
RUN npm run build

# Stage 2: Production Stage with a Lightweight Static Server
FROM node:14-alpine
WORKDIR /app
# Install serve globally
RUN npm install -g serve
# Copy built files from the builder stage
COPY --from=builder /app/build ./build
# Expose an internal port
EXPOSE 5000
# Serve the static files (this container will be accessed internally by the Nginx reverse proxy)
CMD ["serve", "-s", "build", "-l", "5000"]