import React, { useState, useEffect } from 'react';
import { Container, Navbar, Nav, Button, Modal, Alert } from 'react-bootstrap';
import { Routes, Route, Link, useLocation } from 'react-router-dom';
import './App.css';
import EventEdit from './components/Events/EventEdit';
import EventList from './components/Events/EventList';
import EventCreate from './components/Events/EventCreate';
import UsersList from './components/Users/<USER>';
import UserRolesEdit from './components/Users/<USER>';
import DonationList from './components/Donations/DonationList';
import UserInfo from './components/UserInfo';
import Register from './components/Auth/Register';
import Login from './components/Auth/Login';
import { getCurrentUser, logout } from './services/authService';
import ProtectedRoute from './components/ProtectedRoute';
import config from './config';

function Home() {
    const location = useLocation();
    const error = location.state?.error;

    return (
        <div>
            <h2 className="text-center fw-bold mb-4" style={{ fontSize: '2.5rem', color: '#FFA500' }}>Caboolture UCA Admin Portal</h2>
            {error && <Alert variant="danger" className="mt-3">{error}</Alert>}
            <div className="mt-3" style={{ fontSize: '1.2rem', textAlign: 'left', maxWidth: '800px', margin: '0 auto' }}>
              Welcome to the Caboolture UCA Admin Portal. Please log in to continue.
              <br />
              To use the application, you must be a registered user and have the appropriate permissions.
              To do so, please follow the instructions below:
              <br />
              <br />
              1. Register for an account before contacting the system administrator:
              <div style={{ paddingLeft: '20px' }}>
              -please click the "Register" button above.
              </div>
              <br />
              2. Contact the system administrator to get the appropriate permissions.
              <br />
              <br />
              <div style={{ paddingLeft: '20px' }}>
              <strong>Contact:</strong> {config.contact.email}, <strong>Phone:</strong> {config.contact.phone}
              </div>
              <br />
              <div style={{ paddingLeft: '20px' }}>
                Permissions:
                <br />
                a. Executive Admin:
                <div style={{ paddingLeft: '20px' }}>
                  - Can manage all users and roles.
                  <br />
                  - Can manage all events.
                </div>
                b. Admin:
                <div style={{ paddingLeft: '20px' }}>
                  - Can manage all users.
                  <br />
                  - Can manage all events.
                </div>
                c. Moderator:
                <div style={{ paddingLeft: '20px' }}>
                  - Can manage all events.
                </div>
              </div>
              3. Once you have the appropriate permissions, you can log in to the application using the "Login" button above.
            </div>
        </div>
    );
}

function App() {
    const [showRegister, setShowRegister] = useState(false);
    const [showLogin, setShowLogin] = useState(false);
    const [user, setUser] = useState(null);

    const fetchUser = async () => {
        const fetchedUser = await getCurrentUser();
        setUser(fetchedUser);
    };

    useEffect(() => {
        fetchUser();
    }, []);

    const handleCloseRegister = () => setShowRegister(false);
    const handleShowRegister = () => setShowRegister(true);

    const handleCloseLogin = () => setShowLogin(false);
    const handleShowLogin = () => setShowLogin(true);

    const handleLogout = () => {
        logout();
        setUser(null);
    };

    return (
        <div className="App">
            <Navbar bg="dark" variant="dark" expand="lg">
                <Container>
                    <Navbar.Brand as={Link} to="/"><span style={{ color: '#FFA500' }}>CabUCA Admin</span></Navbar.Brand>
                    <Navbar.Toggle aria-controls="basic-navbar-nav" />
                    <Navbar.Collapse id="basic-navbar-nav">
                        <Nav className="me-auto">
                            <Nav.Link as={Link} to="/">Home</Nav.Link>
                            <Nav.Link as={Link} to="/events">Events</Nav.Link>
                            <Nav.Link as={Link} to="/donations">Donations</Nav.Link>
                            <Nav.Link as={Link} to="/users">Users</Nav.Link>
                            <Nav.Link as={Link} to="/roles">Roles</Nav.Link>
                        </Nav>
                        <Nav>
                            {user ? (
                                <>
                                    <Navbar.Text className="me-3">
                                        Signed in as: <a href="#login">{user.Username}</a>
                                        <UserInfo />
                                    </Navbar.Text>
                                    <Button variant="outline-light" onClick={handleLogout}>
                                        Logout
                                    </Button>
                                </>
                            ) : (
                                <>
                                    <Button variant="outline-light" className="me-2" onClick={handleShowLogin}>
                                        Login
                                    </Button>
                                    <Button variant="outline-light" onClick={handleShowRegister}>
                                        Register
                                    </Button>
                                </>
                            )}
                        </Nav>
                    </Navbar.Collapse>
                </Container>
            </Navbar>

            <Container className="mt-4">
                <Routes>
                    <Route path="/" element={<Home onShowLogin={handleShowLogin} onShowRegister={handleShowRegister} />} />
                    <Route path="/events" element={<EventList />} />
                    <Route
                        path="/events/create"
                        element={
                          <ProtectedRoute>
                            <EventCreate />
                          </ProtectedRoute>
                        }
                    />
                    <Route
                        path="/events/edit/:id"
                        element={
                          <ProtectedRoute>
                            <EventEdit />
                          </ProtectedRoute>
                        }
                    />
                    <Route
                        path="/donations"
                        element={
                          <ProtectedRoute>
                            <DonationList />
                          </ProtectedRoute>
                        }
                    />
                    <Route
                        path="/users"
                        element={
                          <ProtectedRoute>
                            <UsersList />
                          </ProtectedRoute>
                        }
                    />
                    <Route
                        path="/users/:userId/edit"
                        element={
                          <ProtectedRoute>
                            <UserRolesEdit />
                          </ProtectedRoute>
                        }
                    />
                    <Route
                      path="/roles"
                      element={
                        <ProtectedRoute>
                          <UserRolesEdit />
                        </ProtectedRoute>
                      } />
                </Routes>
            </Container>

            {/* Register Modal */}
            <Modal
                show={showRegister}
                onHide={handleCloseRegister}
                contentClassName="bg-dark text-light"
                closeButton
                closeVariant="white"
            >
                <Modal.Header closeButton closeVariant="white" className="bg-dark border-0">
                    <Modal.Title>Register</Modal.Title>
                </Modal.Header>
                <Modal.Body className='bg-dark'>
                    <Register onSuccess={fetchUser} />
                </Modal.Body>
            </Modal>

            {/* Login Modal */}
            <Modal
                show={showLogin}
                onHide={handleCloseLogin}
                contentClassName="bg-dark text-light"
                closeButton
                closeVariant="white"
            >
                <Modal.Header closeButton closeVariant="white" className="bg-dark border-0">
                    <Modal.Title>Login</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Login onSuccess={fetchUser} />
                </Modal.Body>
            </Modal>
        </div>
    );
}

export default App;
