import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { getCurrentUserToken } from '../../services/authService';
import { Button, Table, Form } from 'react-bootstrap';
import config from '../../config';

const API_URL = config.api.domain + 'users/';

function UsersList() {
  const [users, setUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  
  const handleDelete = async (userId) => {
  if (window.confirm('Are you sure you want to delete this user?')) {
      const token = getCurrentUserToken();
      await axios.delete(`${API_URL}${userId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      // Refresh users list
      fetchUsers();
    }
  };

  const fetchUsers = async () => {
    const token = getCurrentUserToken();
    const response = await axios.get(`${API_URL}?searchTerm=${searchTerm}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    setUsers(response.data);
  };

  useEffect(() => {
    fetchUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTerm]);

  return (
    <div>
      <h2>Users Management</h2>
      <Form.Control
        type="text"
        placeholder="Search users..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className="mb-3"
      />
      <Table striped bordered hover>
        <thead>
          <tr>
            <th>Username</th>
            <th>Email</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {users.map((user) => (
            <tr key={user.id}>
              <td>{user.userName}</td>
              <td>{user.email}</td>
              <td>
                {/* Redirects to UserRolesEdit.js (routes specified in App.js) */}
                <Button variant="primary" href={`/users/${user.id}/edit`} className="me-2">
                  Edit Roles
                </Button>
                <Button variant="danger" onClick={() => handleDelete(user.id)}>
                  Delete
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </Table>
    </div>
  );
}

export default UsersList;