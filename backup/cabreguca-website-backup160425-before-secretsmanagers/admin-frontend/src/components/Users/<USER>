import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { getCurrentUserToken } from '../../services/authService';
import { Form, Button, Alert } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import config from '../../config';

const API_URL = config.api.domain + 'users/';

function UserRolesEdit() {
  const { userId } = useParams();
  const [roles, setRoles] = useState([]);
  const [allRoles, setAllRoles] = useState([]);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    if (!userId) {
      setError('No user selected');
    } else {
      setError('');
      fetchUserRoles();
      fetchAllRoles();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId]);

  const fetchUserRoles = async () => {
    try {
      const token = getCurrentUserToken();
      const response = await axios.get(`${API_URL}${userId}/roles`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      console.log('User roles:', response.data);
      setRoles(response.data);
    } catch (error) {
      console.error('Error fetching user roles:', error);
    }
  };

  const fetchAllRoles = async () => {
    try {
      const token = getCurrentUserToken();
      const response = await axios.get(`${API_URL}roles`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      console.log('All roles response:', response.data);
      if (Array.isArray(response.data)) {
        setAllRoles(response.data);
      } else {
        console.error('Expected array of roles but got:', response.data);
        setAllRoles([]);
      }
    } catch (error) {
      console.error('Error fetching all roles:', error);
      setAllRoles([]);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const token = getCurrentUserToken();
    await axios.post(`${API_URL}${userId}/roles`, roles, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    navigate('/users');
  };

  const handleRoleChange = (e) => {
    const { value, checked } = e.target;
    if (checked) {
      setRoles([...roles, value]);
    } else {
      setRoles(roles.filter((role) => role !== value));
    }
  };

  return (
    <div className="d-flex flex-column align-items-center">
      <h2 className="mb-4">Edit User Roles</h2>
      {error && <Alert variant="danger" className="mb-3">{error}</Alert>}
      <Form onSubmit={handleSubmit} style={{ width: '300px' }}>
        <div className="d-flex flex-column">
          {allRoles.map((role) => (
            <Form.Check
              key={role}
              type="checkbox"
              label={role}
              value={role}
              checked={roles.includes(role)}
              onChange={handleRoleChange}
              className="mb-2"
              style={{ 
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                textAlign: 'left'
              }}
            />
          ))}
        </div>
        <div className="text-center mt-3">
          <Button variant="primary" type="submit">
            Save Changes
          </Button>
        </div>
      </Form>
    </div>
  );
}

export default UserRolesEdit;