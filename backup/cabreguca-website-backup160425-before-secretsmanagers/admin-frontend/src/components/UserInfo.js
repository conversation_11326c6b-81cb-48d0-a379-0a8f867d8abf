import React, { useState, useEffect } from 'react';
import { getCurrentUserDetails } from '../services/authService';

function UserInfo() {
  const [userDetails, setUserDetails] = useState(null);

  useEffect(() => {
    const fetchUserDetails = async () => {
      const details = await getCurrentUserDetails();
      setUserDetails(details);
    };

    fetchUserDetails();
  }, []);

  if (!userDetails) return null;

  return (
    <span className="text-light">
       {userDetails.userName} 
      {userDetails.roles && userDetails.roles.length > 0 && (
        <span className="ms-2">({userDetails.roles.join(', ')})</span>
      )}
    </span>
  );
}

export default UserInfo;
