import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON>, Badge, Spinner, Alert, Form, InputGroup } from 'react-bootstrap';
import { formatDate, formatCurrency } from '../../utils/formatters';
import { getCurrentUserToken } from '../../services/authService';
import axios from 'axios';
import config from '../../config';

const DonationList = () => {
    const [donations, setDonations] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterStatus, setFilterStatus] = useState('all');

    const fetchDonations = useCallback(async () => {
        try {
            setLoading(true);
            const response = await axios.get(`${config.api.domain}Donations`, {
                headers: {
                    Authorization: `Bearer ${getCurrentUserToken()}`
                }
            });
            setDonations(response.data);
            setError(null);
        } catch (err) {
            console.error('Error fetching donations:', err);
            setError('Failed to load donations. Please try again later.');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchDonations();
    }, [fetchDonations]);

    const getStatusBadge = (status) => {
        switch (status.toLowerCase()) {
            case 'succeeded':
                return <Badge bg="success">Succeeded</Badge>;
            case 'processing':
                return <Badge bg="warning">Processing</Badge>;
            case 'failed':
                return <Badge bg="danger">Failed</Badge>;
            case 'pending':
                return <Badge bg="info">Pending</Badge>;
            default:
                return <Badge bg="secondary">{status}</Badge>;
        }
    };

    const filteredDonations = donations.filter(donation => {
        const matchesSearch =
            (donation.donorName && donation.donorName.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (donation.donorEmail && donation.donorEmail.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (donation.purpose && donation.purpose.toLowerCase().includes(searchTerm.toLowerCase()));

        const matchesStatus = filterStatus === 'all' || donation.paymentStatus.toLowerCase() === filterStatus.toLowerCase();

        return matchesSearch && matchesStatus;
    });

    if (loading) {
        return (
            <div className="text-center my-5">
                <Spinner animation="border" role="status">
                    <span className="visually-hidden">Loading...</span>
                </Spinner>
            </div>
        );
    }

    if (error) {
        return (
            <Alert variant="danger">
                {error}
                <Button
                    variant="outline-danger"
                    size="sm"
                    className="ms-3"
                    onClick={fetchDonations}
                >
                    Try Again
                </Button>
            </Alert>
        );
    }

    return (
        <div className="donation-list">
            <h2 className="mb-4">Donations</h2>

            <div className="filters mb-4">
                <div className="row">
                    <div className="col-md-6 mb-3">
                        <InputGroup>
                            <Form.Control
                                placeholder="Search by name, email, or purpose..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                            {searchTerm && (
                                <Button
                                    variant="outline-secondary"
                                    onClick={() => setSearchTerm('')}
                                >
                                    Clear
                                </Button>
                            )}
                        </InputGroup>
                    </div>
                    <div className="col-md-4 mb-3">
                        <Form.Select
                            value={filterStatus}
                            onChange={(e) => setFilterStatus(e.target.value)}
                        >
                            <option value="all">All Statuses</option>
                            <option value="succeeded">Succeeded</option>
                            <option value="processing">Processing</option>
                            <option value="pending">Pending</option>
                            <option value="failed">Failed</option>
                        </Form.Select>
                    </div>
                    <div className="col-md-2 mb-3">
                        <Button
                            variant="outline-primary"
                            className="w-100"
                            onClick={fetchDonations}
                        >
                            Refresh
                        </Button>
                    </div>
                </div>
            </div>

            {filteredDonations.length === 0 ? (
                <Alert variant="info">
                    No donations found matching your criteria.
                </Alert>
            ) : (
                <div className="table-responsive">
                    <Table striped bordered hover>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Donor</th>
                                <th>Purpose</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {filteredDonations.map(donation => (
                                <tr key={donation.id}>
                                    <td>{donation.id}</td>
                                    <td>{formatDate(donation.donationDate)}</td>
                                    <td>{formatCurrency(donation.amountInCents / 100, donation.currency)}</td>
                                    <td>
                                        {donation.isAnonymous ? (
                                            <em>Anonymous</em>
                                        ) : (
                                            <>
                                                {donation.donorName}<br />
                                                <small className="text-muted">{donation.donorEmail}</small>
                                            </>
                                        )}
                                    </td>
                                    <td>{donation.purpose || 'General Donation'}</td>
                                    <td>{getStatusBadge(donation.paymentStatus)}</td>
                                </tr>
                            ))}
                        </tbody>
                    </Table>
                </div>
            )}
        </div>
    );
};

export default DonationList;
