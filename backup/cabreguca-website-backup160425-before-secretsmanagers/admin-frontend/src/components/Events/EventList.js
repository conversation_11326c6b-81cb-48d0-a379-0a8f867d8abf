import React, { useEffect, useState } from 'react';
import { Table, Button, Alert } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { deleteEvent } from '../../services/eventService';
import { getCurrentUserToken } from '../../services/authService';
import config from '../../config';

// API_URL is defined in config.js (the REST API URL)
const API_URL = config.api.domain + 'events/';

function EventList() {
  const [events, setEvents] = useState([]);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      const token = getCurrentUserToken();
      const response = await axios.get(`${API_URL}admin`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setEvents(response.data);
      console.log('Fetched events:', response.data); // Debugging line
    } catch (error) {
      console.error('Error fetching events:', error);
      setError('Failed to fetch events');
    }
  };

  const deleteEventAction = async (id) => {
    if (window.confirm('Are you sure you want to delete this event?')) {
      try {
        await deleteEvent(id);
        await fetchEvents(); // Refresh the list after deletion
      } catch (error) {
        console.error('Error deleting event:', error);
        setError('Failed to delete event. Please try again.');
      }
    }
  };

  const handleEdit = (id) => {
    if (id && id !== 0) { // Ensure id is valid
      navigate(`/events/edit/${id}`);
    } else {
      alert('Invalid event ID. Cannot edit this event.');
    }
  };

  return (
    <div>
      <h2>Events</h2>
      {error && <Alert variant="danger">{error}</Alert> }
      <Button as={Link} to="/events/create" variant="primary" className="mb-3">
        Create New Event
      </Button>
      <Table striped bordered hover className="text-light bg-dark">
        <thead className="text-light bg-dark">
          <tr>
            <th>ID</th>
            <th>Title</th>
            <th>Date</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {events.map((event) => (
            <tr key={event.id}>
              <td>{event.id}</td>
              <td>{event.title}</td>
              <td>{new Date(event.date).toLocaleDateString()}</td>
              <td>
                <Button
                  variant="warning"
                  onClick={() => handleEdit(event.id)}
                  className="me-2"
                >
                  Edit
                </Button>
                <Button
                  variant="danger"
                  onClick={() => deleteEventAction(event.id)}
                >
                  Delete
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </Table>
    </div>
  );
}

export default EventList;
