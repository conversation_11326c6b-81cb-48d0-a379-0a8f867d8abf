load_module modules/ngx_http_modsecurity_module.so;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=6r/s;

    # ModSecurity Configuration
    modsecurity on;
    modsecurity_rules_file /etc/nginx/modsec/main.conf;

    # Redirect HTTP to HTTPS for ${DOMAIN_NAME}
    server {
        listen 80;
        server_name ${DOMAIN_NAME};

        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
        }

        location / {
            return 301 https://$host$request_uri;
        }
    }

    # Redirect HTTP to HTTPS for admin.${DOMAIN_NAME}
    server {
        listen 80;
        server_name ${ADMIN_DOMAIN_NAME};

        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
        }

        location / {
            return 301 https://$host$request_uri;
        }
    }

    # HTTPS server for ${DOMAIN_NAME}
    server {
        listen 443 ssl;
        server_name ${DOMAIN_NAME};

        ssl_certificate /etc/letsencrypt/live/${DOMAIN_NAME}/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/${DOMAIN_NAME}/privkey.pem;

        include /etc/nginx/ssl/options-ssl-nginx.conf;
        ssl_dhparam /etc/nginx/ssl/ssl-dhparams.pem;

        # Proxy API Requests
        location /api/ {
            limit_req zone=api_limit burst=10;
            proxy_pass http://api:8080/api/;
            proxy_http_version 1.1;

            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection keep-alive;
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location / {
            limit_req zone=api_limit burst=10;
            proxy_pass http://api:8080/;  # Update with the correct service for the main site
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        location ~* ^/(docker|swam|services|tasks|networks)/ {
            deny all;
            return 404;
        }

        location /containers/ {
            deny all;
            return 404;
        }
    }
        
    server {
        listen 443 ssl;
        server_name ${ADMIN_DOMAIN_NAME};

        ssl_certificate /etc/letsencrypt/live/${ADMIN_DOMAIN_NAME}/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/${ADMIN_DOMAIN_NAME}/privkey.pem;
        include /etc/nginx/ssl/options-ssl-nginx.conf;
        ssl_dhparam /etc/nginx/ssl/ssl-dhparams.pem;

        # Proxy Admin Frontend
        location / {
            limit_req zone=api_limit burst=10;
            proxy_pass http://admin-frontend:5000/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        location ~* ^/(docker|swam|services|tasks|networks)/ {
            deny all;
            return 404;
        }

        location /containers/ {
            deny all;
            return 404;
        }
    }
}

events {
    worker_connections 1024;
}
