   server {
       listen 80;
       server_name ${DOMAIN_NAME};

       location /.well-known/acme-challenge/ {
           root /var/www/certbot;
       }

       location / {
           return 301 https://$host$request_uri;
       }
   }

   server {
       listen 443 ssl;
       server_name ${DOMAIN_NAME};

       ssl_certificate /etc/letsencrypt/live/${DOMAIN_NAME}/fullchain.pem;
       ssl_certificate_key /etc/letsencrypt/live/${DOMAIN_NAME}/privkey.pem;

       include /etc/nginx/ssl/options-ssl-nginx.conf;
       ssl_dhparam /etc/nginx/ssl/ssl-dhparams.pem;

       location / {
           proxy_pass http://api:8080;
           proxy_http_version 1.1;
           proxy_set_header Connection "";
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
