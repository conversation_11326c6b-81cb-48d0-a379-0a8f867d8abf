services:
  # # Migrations: Handle database migrations using a prebuilt image
  # migrations:
  #   image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/olmate/cabuca-migrations:latest
  #   environment:
  #     - ConnectionStrings__DefaultConnection=Data Source=/Data/CabUCADb.sqlite
  #     - ASPNETCORE_ENVIRONMENT=Production
  #     - DATA_PROTECTION_CERT_PATH=/certs/dp_keys.pfx
  #     - DATA_PROTECTION_CERT_PASSWORD=${DATA_PROTECTION_CERT_PASSWORD}
  #   volumes:
  #     - sqlite-data:/Data
  #     - /certs/dp_keys.pfx:/certs/dp_keys.pfx:ro
  #     - data-protection-keys:/app/.aspnet/DataProtection-Keys
  #   env_file:
  #     - .env

  # API: Your ASP.NET Core application
  api:
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/olmate/cabuca-api:latest
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=https://+:8443;http://+:8080
      - ConnectionStrings__DefaultConnection=Data Source=/Data/CabUCADb.sqlite
      - JWT_SECRET=${JWT_SECRET}
      - ADMIN_USERNAME=${ADMIN_USERNAME}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD}
      - ADMIN_EMAIL=${ADMIN_EMAIL}
    volumes:
      - sqlite-data:/Data
      - data-protection-keys:/app/.aspnet/DataProtection-Keys
      #for faster rebuilds:
      - ~/.nuget/packages:/root/.nuget/packages
      - ./certs:/certs
    expose:
      - "8080"
      - "8443"
    env_file:
      - .env
    networks:
      - backend
    depends_on:
      migrations:
        condition: service_completed_successfully

  # Admin Frontend: Your Admin React Application
  admin-frontend:
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/olmate/cabuca-admin
    environment:
      - DOMAIN_NAME=${ADMIN_DOMAIN_NAME}
      - REACT_APP_API_DOMAIN_NAME=${REACT_APP_API_DOMAIN_NAME}
      - REACT_APP_DOMAIN_NAME=${REACT_APP_DOMAIN_NAME}
    volumes:
      - certbot-etc:/etc/letsencrypt
      - certbot-www:/var/www/certbot
    expose:
      - "5000"
    env_file:
      - .env
    networks:
      - backend
    depends_on:
      - api

  # Nginx: Reverse proxy and SSL termination
  nginx:
    image: owasp/modsecurity-nginx:latest
    environment:
      - DOMAIN_NAME=${DOMAIN_NAME}
      - ADMIN_DOMAIN_NAME=${ADMIN_DOMAIN_NAME}
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - certbot-etc:/etc/letsencrypt
      - certbot-www:/var/www/certbot
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/templates/nginx-https.conf:/etc/nginx/conf.d/nginx.conf
      - ./nginx/logs:/var/log/nginx
      - ./nginx/modsecurity-custom:/etc/modsecurity.d/custom-rules:ro
      - nginx-pid:/var/run/nginx
    networks:
      - backend
    depends_on:
      - api
      - admin-frontend

  # Certbot: Obtain and renew Let's Encrypt certificates
  certbot:
    image: certbot/certbot
    volumes:
      - certbot-etc:/etc/letsencrypt
      - certbot-www:/var/www/certbot
    networks:
      - backend

volumes:
  certbot-etc:
  certbot-www:
  sqlite-data:
  data-protection-keys:
  #nginx-pid:
networks:
  backend:
    driver: bridge
