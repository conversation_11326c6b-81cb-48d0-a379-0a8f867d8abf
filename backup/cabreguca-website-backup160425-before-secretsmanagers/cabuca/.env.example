# .env.example

# WARNING: make sure these values are changed before using the application.
# do not commit this file with password values to the repository, as it contains sensitive information!
# this file is only an example, it is not used by the application itself. Please make sure to create your own .env file.
# (or remove the .example extension from this file.) Make sure this file is in .gitignore (set to ignore .env)

# This file is used to set the environment variables for the application
# It is used by the docker-compose.yml file to set the environment variables for the container
# It is also used by the Dockerfile to set the environment variables for the application
# It is not used by the application itself

# make sure you use a docker credential helper to store your dockerhub password. do not use the DOCKER_PASSWORD variable.


# .env:

# backend api app environment variables:
ASPNETCORE_ENVIRONMENT=Production
# change this value to your secure jwt secret key
JWT_SECRET=your_secure_jwt_secret_key 

# admin user credentials
ADMIN_USERNAME=admin
# change this value to your admin password
ADMIN_PASSWORD=your_admin_password 
# change this value to your admin email
ADMIN_EMAIL=<EMAIL>

# .env.Production, only if using dockerhub (keep this commented out, use ecr-credential-helper instead)
# DOCKER_USERNAME=your-dockerhub-username

# SSH credentials for remote deployment:
# NOTE: currently not used in this version of the application (commented out)
# SSH_PRIVATE_KEY=your-ssh-private-key
# SSH_USERNAME=your-ssh-username
# SSH_SERVER_IP=your-ssh-server-ip
# SSH_SERVER_PATH=your-ssh-server-path

# Data Protection Keys
# change this value to your data protection cert password
DATA_PROTECTION_CERT_PASSWORD=your_data_protection_cert_password 
# change this value to the path of the data protection cert
DATA_PROTECTION_CERT_PATH=../certs/dp_keys.pfx

# REACT_APP environment variables (for admin-frontend organisation details)
# change this value to your organisation name
REACT_APP_ORGANISATION_NAME="your organisation name" 
# change this value to your organisation full name
REACT_APP_ORGANISATION_FULL_NAME="your organisation full name" 
# change this value to your contact email
REACT_APP_CONTACT_EMAIL="your contact email" 
# change this value to your contact address
REACT_APP_CONTACT_ADDRESS="your contact address" 
# This will be used to make API calls to the backend
REACT_APP_API_DOMAIN_NAME=your_api_domain_name.com 
# for REACT_APP_API_DOMAIN_NAME, use the domain name of the backend api in the formate: yourdomain.com
# no http:// or https:// prefixes or suffixes!
# This is the base url of the admin frontend react app
REACT_APP_DOMAIN_NAME=your_base_url.com
# same as above, not prefixed or suffixed!

# TinyMCE API Key for the admin frontend rich text editor for the pages content editor
TINYMCE_API_KEY=your_tinymce_api_key

# Let's Encrypt environment variables (for production)
# change this value to your domain name
DOMAIN_NAME=your_domain_name 
# change this value to your admin domain name
ADMIN_DOMAIN_NAME=admin.your_domain_name 
# change this value to your email
EMAIL=<EMAIL>

# Let's Encrypt configuration
# change this value to your domain name
DOMAIN_NAME=your_domain_name 
# change this value to your admin domain name
ADMIN_DOMAIN_NAME=admin.your_domain_name 
# change this value to your email
EMAIL=<EMAIL>
# change this value to the size of the RSA key
RSA_KEY_SIZE=4096
# wether to use staging or production letsencrypt
# WARNING! production certs have a rate limit of 5 certs per week and a limit of 5000 certs per account!
# staging is unlimited, but the certs are not trusted by default.
# change this value to 1 to use staging, or 0 to use production
STAGING=1
# change this value to the path of the data protection cert
DATA_PATH=./certbot