# Stripe Integration Setup

This document provides instructions for setting up and testing the Stripe donation integration for the CabUCA church website.

## Prerequisites

- [.NET 8 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- [Stripe CLI](https://stripe.com/docs/stripe-cli#install)
- A Stripe account with API keys

## Setup

### 1. Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# Stripe API Keys (Test Mode)
STRIPE_SECRET_KEY=sk_test_your_test_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_test_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# JWT Settings
JWT_SECRET=your_jwt_secret

# Admin User Credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_admin_password
ADMIN_EMAIL=<EMAIL>

# Environment Settings
ASPNETCORE_ENVIRONMENT=Development
DEV_HTTPS=true
```

Replace the placeholder values with your actual Stripe API keys.

### 2. Database Migration

The application will automatically apply migrations at startup, including the Donations table migration.

### 3. Running the Application

Start the backend API:

```bash
cd backend/CabUCA.API/CabUCA.API
dotnet run
```

The API will be available at:
- HTTP: http://localhost:8080
- HTTPS: https://localhost:8443 (if HTTPS is enabled)

### 4. Testing with Stripe CLI

1. Install the Stripe CLI: https://stripe.com/docs/stripe-cli#install

2. Log in to your Stripe account:
   ```bash
   stripe login
   ```

3. Start webhook forwarding:
   ```bash
   stripe listen --forward-to http://localhost:8080/api/donations/webhook
   ```
   This will output a webhook signing secret that you should add to your `.env` file.

4. Test the payment flow by visiting the donation page:
   http://localhost:8080/donate

5. Use Stripe test cards for testing:
   - Success: 4242 4242 4242 4242
   - Requires Authentication: 4000 0025 0000 3155
   - Declined: 4000 0000 0000 9995

## Automated Testing

You can use the provided test script to verify the Stripe integration:

```bash
./test-stripe.sh
```

This script will:
1. Start the backend API
2. Start Stripe webhook forwarding
3. Create a test payment intent
4. Clean up after testing

## Production Deployment

For production deployment:

1. Update the `.env` file with production Stripe API keys
2. Configure a real webhook endpoint in the Stripe Dashboard
3. Set up proper error handling and monitoring

## Troubleshooting

- Check the application logs for any errors
- Verify that the Stripe API keys are correct
- Ensure the webhook secret is properly configured
- Check the Stripe Dashboard for webhook delivery status
