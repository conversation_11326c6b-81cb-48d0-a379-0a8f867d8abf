/* Ensure consistent box sizing for all elements */
*, *::before, *::after {
    box-sizing: border-box;
}

/* Reset all elements */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Global CSS Variables */
        /* distance from top of the page to the navbar (padding-top in #main-content style) */
    --navbar-height: 20px; 
    --main-content-padding-large: 20px;
    --main-content-padding-medium: 15px;
    --main-content-padding-small: 4px;
    --title-color: #c3a633;
    --title-font-type: 'Georgia';  /* Georgia pairs well with Trebuchet MS - it's a classic serif that complements Trebuchet's modern sans-serif style */
    --primary-color: #ff5100;
    --primary-dark: #923003;
    --secondary-color: #1d0902;
    --text-color: #fff;
    --body-text-color: #000;
    --margins: 0.8rem;
}

body {
    font-family: Arial, sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    min-height: 100vh;
    background-image: url('../data/img/bkg/CRUC_Logo_and_trim.webp');
    background-attachment: scroll;
    background-size: 100vw auto; /* Width fits screen, height scales automatically */
    background-position: center top;
    background-repeat: no-repeat;
    background-blend-mode: overlay;
    background-color: rgb(20, 17, 17);
    margin: 0;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
}

/* Media query for mobile devices */
@media screen and (max-width: 768px) {
    body {
        background-size: 100vw auto; /* Same for mobile - maintains aspect ratio */
    }
}

/* Remove the problematic pseudo-element */
@media screen and (min-aspect-ratio: 1/2) {
    body::after {
        display: none; /* Disable this entirely */
    }
}

/* Main Content Area */
#main-content {
    flex: 1;
    width: 85%;
    max-width: 1300px;
    margin-top: var(--margins);          /* Add space between navbar and content */
    margin-bottom: var(--margins);       /* Add space between content and footer */
    padding-top: var(--navbar-height);
    padding-left: var(--main-content-padding-large);
    padding-right: var(--main-content-padding-large);
    background: rgba(219, 219, 219, 0.076);  /* Subtle white background for glass effect */
    backdrop-filter: blur(5px);           /* Creates frosted glass effect */
    -webkit-backdrop-filter: blur(10px);   /* For Safari support */
    border: 1px solid rgba(255, 255, 255, 0.2);  /* Subtle border */
    box-shadow: 
        0 4px 6px rgba(0, 0, 0, 0.1),
        inset 0 0 20px rgba(255, 255, 255, 0.1); /* Inner glow */
    border-radius: 8px;
}

/* Responsive Adjustments */

/* Tablets and Small Desktops */
@media screen and (min-width: 481px) and (max-width: 768px) {
    #main-content {
        width: 90%;
        padding-left: var(--main-content-padding-medium);
        padding-right: var(--main-content-padding-medium);
    }
}

/* Mobile Devices */
@media screen and (max-width: 480px) {
    #main-content {
        width: 100%;
        padding-left: var(--main-content-padding-small);
        padding-right: var(--main-content-padding-small);
    }
}

.content-container {
    max-width: 1100px;
    margin: var(--margins) auto;
    padding: 1.5rem;
    background-color: #0000009f;
    color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    font-weight: 500;
    font-family:'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
}

/* Link Styles */
a {
    text-decoration: underline;
    color: var(--title-color);
}

a:hover {
    color: var(--primary-color);
}

/* Link Styles for buttons */
.button-link {
    display: inline-block;
    padding: 2px 12px;
    background-color: var(--primary-color);
    color: #fff;
    text-decoration: none;
    border-radius: 3px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    cursor: pointer;
}

.button-link:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Underline animation on hover */
.button-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: var(--accent-color);
    transition: width 0.3s ease;
}

.button-link:hover::after {
    width: 100%;
}

/* Active/clicked links */
.button-link:active {
    transform: scale(0.98);
}

.button-link-large {
    padding: 10px 20px;
    font-size: 1.2rem;
    font-weight: bold;
    color: #ffffff; /* Light gold color */
}

@media screen and (max-width: 768px) {
    .button-link-header-nav {
        width: 100%;
        margin: 1px auto;
        background: radial-gradient(circle at center, #fe8700 0%, var(--primary-color) 100%);
    }

    .button-link-header-nav:hover {
        background: radial-gradient(circle at center, #5f2b01 0%, var(--primary-color) 100%);
        transform: translateY(-2px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        font-color: rgb(247, 214, 32);
    }
}



/* Loader Overlay Styles */
.loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.359); /* Semi-transparent background */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000; /* Ensure it covers all other content */
  transition: opacity 0.5s ease;
}

.loader {
  text-align: center;
  font-family: Arial, sans-serif;
  color: #ffffff;
}

.spinner {
  border: 6px solid #f3f3f3;
  border-top: 6px solid #3498db;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Popup Styles */
.error-popup {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 15px 20px;
    border-radius: 5px;
    z-index: 20000;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    max-width: 300px;
    display: none; /* Hidden by default */
}

.error-popup p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.3;
}

.error-popup-close {
    position: absolute;
    top: 5px;
    right: 10px;
    cursor: pointer;
    font-size: 20px;
    color: #fff;
}

/* Scroll to Top Button Styles */
#scroll-to-top {
    position: fixed;
    bottom: 40px;
    right: 40px;
    z-index: 1000;
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    outline: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 24px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    opacity: 0.7;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

#scroll-to-top:hover {
    opacity: 1;
    transform: translateY(-5px);
}

/* On mobile devices, make the button more transparent */
@media screen and (max-width: 767px) {
    #scroll-to-top {
         opacity: 0.4;
    }
    
    /* If a hover effect is applicable (for tablets, etc.), slightly increase opacity */
    #scroll-to-top:hover {
         opacity: 0.7;
    }
}

