/* Add at the top to ensure consistent box model */
* {
    box-sizing: border-box;
}

:root {
    /* Fluid width calculations */
    --calendar-width: min(100%, 650px);      /* Desktop default - changed to 100% max width */
    --calendar-width-mobile: min(100%, 480px); /* Mobile width - changed to 100% max width */
    --base-unit: 1rem;                         /* Base unit for scaling */
    --day-ratio: calc(var(--base-unit) * 3);   /* 48px equivalent */
    --event-indicator-color: rgba(255, 98, 0, 0.74); /* Default orange for recurring events */
    --special-event-indicator-color: rgba(255, 98, 0, 0.74); /* Changed to match orange for all events */
    --sidebar-width: 300px;                    /* Width for the sidebar */
    --calendar-padding: 20px;                  /* Calendar padding - used in calculations */
    --calendar-padding-mobile: 10px;           /* Mobile calendar padding */
}

/* New layout structure */
.main-layout-container {
    display: flex;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
    overflow: hidden; /* Prevent overflow from children */
}

.left-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-width: 100%; /* Ensure it doesn't exceed container width */
    min-width: 0; /* Allow flex items to shrink below content size */
}

.right-column {
    width: var(--sidebar-width);
    min-width: var(--sidebar-width);
}

/* Update calendar container styles */
.calendar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin: 0;
    padding: 0;
    border-radius: 8px;
    overflow: visible; /* Changed from hidden to visible */
    color: white;
    max-width: 100%; /* Ensure it doesn't exceed parent width */
    box-sizing: border-box; /* Include padding and border in width calculations */
}

/* Calendar Styles */
.calendar {
    width: 100%;
    max-width: 100%; /* Ensure calendar doesn't exceed container width */
    background-color: #000;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    padding: var(--calendar-padding);
    margin-bottom: var(--margins);
    color: white;
    box-sizing: border-box; /* Include padding in width calculations */
}

#refresh {
    width: 100%;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: var(--text-color);
    border: none;
    border-radius: 8px 8px 0 0;  /* Match top corners with container */
    cursor: pointer;
    margin-bottom: 0;
    font-weight: bold;
    transition: all 0.3s ease;
    position: relative;
    transform: translateY(0);
    filter: saturate(100%) brightness(100%);
    box-shadow: inset 0 1px 0 rgba(255,255,255,0.2);  /* Subtle inner highlight */
}

#refresh:disabled {
    cursor: not-allowed;
    transform: translateY(0);  /* Remove vertical movement */
    filter: saturate(40%) brightness(90%);
    background-color: var(--primary-color);
    opacity: 0.8;
}

#refresh.throttled {
    background-color: var(--primary-dark);
    filter: saturate(40%) brightness(80%);
    animation: pulseButton 2s ease infinite;
}

#refresh:hover:not(:disabled) {
    background-color: var(--primary-dark);
    filter: brightness(95%);
}

@keyframes pulseButton {
    0% { filter: saturate(40%) brightness(80%); }
    50% { filter: saturate(30%) brightness(70%); }
    100% { filter: saturate(40%) brightness(80%); }
}

.calendar-header {
    display: grid;
    grid-template-columns: 60px 1fr 60px;
    align-items: center;
    margin-bottom: 20px;
    text-align: center;
}

.calendar-header button {
    background-color: var(--primary-color);
    border: none;
    color: var(--text-color);
    padding: 10px;
    border-radius: 10px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.calendar-header button:hover {
    background-color: var(--primary-dark);
}

#month-year {
    font-size: 1.2em;
    font-weight: bold;
}

.weekdays {
    display: grid;
    grid-template-columns: repeat(7, minmax(0, 1fr)); /* Match days grid */
    text-align: center;
    width: 100%;
    grid-gap: 0;
    box-sizing: border-box;
}

.days {
    display: grid;
    grid-template-columns: repeat(7, minmax(0, 1fr)); /* Use minmax to prevent overflow */
    grid-auto-rows: 1fr; /* Ensure all rows have equal height */
    text-align: center;
    width: 100%;
    grid-gap: 0;
    border: 1px solid #333;
    border-top: none;
    aspect-ratio: auto; /* Remove default aspect-ratio */
    box-sizing: border-box;
}

.weekdays div {
    font-weight: bold;
    padding: 10px 0;
    background-color: #000;
    border-bottom: 2px solid #333;
    color: white;
    width: 100%;
    box-sizing: border-box;
}

.days div {
    min-height: 40px;
    height: auto; 
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    border-top: 1px solid #333;
    border-left: 1px solid #333;
    cursor: pointer;
    color: white;
    padding: 5px 5px 5px 5px;
    padding-top: 28px;
    background-color: #000;
    overflow: hidden;
}

/* Remove right border from last column */
.days div:nth-child(7n) {
    border-right: none;
}

/* Remove bottom border from last row */
.days div:nth-last-child(-n+7) {
    border-bottom: none;
}

.days div:hover {
    background-color: #3f3f3f;
}

/* empty days */
.days div.empty {
    background-color: #111;
    cursor: default;
}

.day {
    position: relative; /* Ensure the .day has a stacking context */
    z-index: 1;         /* Higher z-index to place text above the indicator */
}

.day:hover {
    background-color: rgba(255, 98, 0, 0.371); /* Light orange overlay on hover */
}

/* Event indicator styles - fill the entire day cell */
.event-indicator {
    position: absolute;
    top: 0; /* Start from the top of the cell */
    left: 0; /* Start from the left of the cell */
    right: 0; /* Extend to the right edge */
    bottom: 0; /* Extend to the bottom edge */
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    background-color: var(--event-indicator-color);
    border-radius: 8px; /* Rounded corners */
    z-index: 0; /* Place behind the day number */
    opacity: 0.4;
    pointer-events: none; /* Ensure clicks pass through to the day cell */
}

/* Day number styles - ensure visibility over the background */
.day-number {
    position: relative;
    z-index: 2;
    pointer-events: none;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.6); /* Darker background for contrast */
    border-radius: 50%;
    width: 24px;
    height: 24px;
    margin-top: 5px; /* Add space from top */
    margin-bottom: 0; /* Remove bottom margin */
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.5); /* Add shadow for better visibility */
}

/* Today's day number */
.day.today .day-number {
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    padding: 0;
    color: var(--primary-color);
    background-color: rgba(0, 0, 0, 0.8); /* Darker background for better visibility */
    width: 26px;
    height: 26px;
    box-sizing: border-box;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.7); /* Add shadow for better visibility */
}

/* Remove any existing background styles if present */
.day.today {
    background-color: transparent; /* Ensure no background color */
}

/* Updated .event-indicator styles */
.event-indicator.special-event {
    background-color: var(--event-indicator-color); /* Changed to use the same orange color */
    opacity: 0.4; /* Match the same opacity */
}

/* event popup container */
.event-popup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    align-self: auto;   /* Center the popup (overwriting the flex container from body in styles.css) */
    justify-self: auto; /* Center the popup (overwriting the flex container from body in styles.css) */
    background-color: #000000;
    color: #fff;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.2);
    z-index: 1000;
    width: min(300px, 90vw);
    max-height: 80vh;
    overflow-y: auto;
    transition: opacity 0.3s ease;
}

/* event popup overlay */
.overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6); /* Increased opacity for better visibility */
    z-index: 999;
    transition: opacity 0.3s ease;
}

/* Show Popup */
.event-popup.show,
.overlay.show {
    display: block;
    opacity: 1;
}

.event-popup h3 {
    font-size: 1.2em;
    margin-top: 0;
    padding-right: 30px;
}

.event-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.event-item:last-child {
    border-bottom: none;
}

.event-popup-close {
    position: sticky;
    top: 0;
    right: 0;
    padding: 10px;
    cursor: pointer;
    background: none;
    border: none;
    font-size: 40px;
    color: #666;
    float: right;
    z-index: 1001;
}

.event-popup-close:hover {
    color: #333;
}

/* calendar layout for side pane and calendar */
.calendar-layout {
    display: flex;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0px;
}

.events-side-pane {
    width: 100%;
    background: var(--secondary-color);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    height: 100%;
    position: sticky;
    top: 20px;
}

.events-side-pane h3 {
    color: white;
    margin-bottom: 15px;
}

/* month events list in side pane */
.month-events-list {
    margin-top: 10px;
}

.month-event-item {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #333; /* Darker background */
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    color: white;
    border-left: 4px solid transparent;
}

.month-event-item:hover {
    background-color: #444; /* Slightly lighter on hover */
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Recurring event styling */
.month-event-item.recurring {
    background-color: rgba(255, 98, 0, 0.15);
    border-left: 4px solid var(--event-indicator-color);
}

.month-event-item.recurring:hover {
    background-color: rgba(255, 98, 0, 0.25);
}

/* Non-recurring (special) event styling */
.month-event-item:not(.recurring) {
    background-color: rgba(195, 70, 51, 0.195);
    border-left: 4px solid var(--primary-dark);
}

.month-event-item:not(.recurring):hover {
    background-color: rgba(195, 166, 51, 0.25);
}

/* Event date and time styling */
.event-date-time {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Default date/time color for general events */
.event-date, .event-time {
    font-size: 0.9em;
    font-weight: bold;
    color: #ff6200; /* Default color for recurring events */
}

/* Special event date/time color */
.month-event-item:not(.recurring) .event-date,
.month-event-item:not(.recurring) .event-time {
    color: var(--title-color, #c3a633); /* Match the gold color for special events */
}

.event-title {
    margin: 5px 0;
    font-size: 1.1em;
    color: white;
    font-weight: bold;
}

.event-description {
    font-size: 0.9em;
    margin-bottom: 5px;
    color: rgba(255, 255, 255, 0.8);
}

.event-location, .event-recurrence {
    font-size: 0.8em;
    margin-bottom: 3px;
    color: rgba(255, 255, 255, 0.7);
}

.recurrence-icon {
    display: inline-block;
    margin-right: 4px;
}

/* Event popup time display */
.event-item p {
    margin: 5px 0;
}

.event-popup .event-time {
    font-weight: bold;
    color: #ff6200;
}

/* Event section titles */
.event-section-title {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 1.2em;
    color: #ff9d4f; /* Light orange */
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 5px;
}

/* Mobile responsive adjustments */
@media screen and (max-width: 968px) {
    .main-layout-container {
        flex-direction: column;
        padding: 0 10px;
    }
    
    .right-column {
        width: 100%;
        min-width: 100%;
    }
    
    .special-events-container,
    .calendar-container {
        width: 100%; 
        max-width: 100%;
    }
    
    .events-side-pane {
        width: 100%;
        margin-top: 20px;
    }
    
    .calendar {
        padding: 10px;
    }
    
    .days div {
        min-height: calc(var(--day-ratio) * 0.8); /* Smaller day cells on mobile */
    }
}

/* Loading Spinner */
.loading-spinner {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

/* spinner animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Screen sizes */

/* Medium screens */
@media screen and (min-width: 768px) {

    .calendar-header {
        grid-template-columns: 60px 1fr 60px;
    }

    .weekdays div {
        font-size: 1.2em;
        padding: 20px 20px;
    }

    #month-year {
        font-size: 1.1em;
    }

    .days div {
        padding: 20px 0;
        font-size: 0.9em;
        /* height of the calendar days */
        min-height: 80px;
    }

    .event-popup h3 {
        font-size: 1.2em;
    }

    .event-item {
        font-size: 0.9em;
    }
}

/* Mobile Devices */
@media screen and (max-width: 767px) {
    .calendar-header {
        grid-template-columns: 60px 1fr 60px;
    }

    .weekdays div {
        font-size: 0.9em;
        padding: 5px 0;
    }

    #month-year {
        font-size: 1.1em;
    }

    .days div {
        padding: 10px 0;
        font-size: 0.9em;
    }

    .event-popup h3 {
        font-size: 1.2em;
    }

    .event-item {
        font-size: 0.9em;
    }
}

/* Extra small screens */
@media screen and (max-width: 320px) {
    .calendar {
        padding: 5px;
    }

    .days div {
        padding: 8px 0;
        font-size: 0.8em;
    }
    
    #refresh {
        padding: 6px 5px;  /* Match horizontal padding */
    }
}

/* Additional responsive adjustments for very small screens */
@media screen and (max-width: 480px) {
    .calendar {
        padding: 10px 5px;
    }
    
    .days div {
        min-height: calc(var(--day-ratio) * 0.7); /* Even smaller day cells */
        font-size: 0.9rem;
    }
    
    #refresh {
        padding: 8px;
        font-size: 0.9rem;
    }
}

/* Excluded dates */
.day.excluded {
    background-color: #f8d7da;
    cursor: not-allowed;
}

.day.excluded .day-number {
    color: #721c24;
}

.event-indicator.excluded {
    display: none;
}

.event-list {
    max-height: 60vh; /* Restrict the height */
    overflow-y: auto; /* Enable vertical scrolling */
    padding-right: 10px; /* Space for scrollbar */
}

.month-event-item.recurring {
    background-color: rgba(255, 243, 224, 0.1);
    border-left: 4px solid #fb8c00;
}

.month-event-item.recurring .month-event-recurrence {
    font-style: italic;
    color: #fb8c00;
}

/* Optionally, add an icon for recurring events */
.month-event-item.recurring .month-event-recurrence::before {
    content: "🔄";
    margin-right: 4px;
    display: inline-block;
}

/* for upcoming and past events */
.event-section-title {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 1.2em;
    color: #ffd793; /* Use your primary color variable */
    border-bottom: 2px solid #ddd;
    padding-bottom: 5px;
}

/* Special Events Styling */
.special-events-container {
    width: 100%;
    margin: 0;
    padding: 20px;
    background-color: var(--secondary-color); /* Darker background */
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.special-events-container h3 {
    color: var(--title-color, #ff9d4f); /* Use title-color variable with fallback */
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #444;
    text-align: center;
    font-size: 1.5em;
}

.special-events-list {
    display: flex;
    flex-direction: row; /* Changed from column to row for side-by-side layout */
    flex-wrap: wrap; /* Allow items to wrap to next row */
    gap: 15px;
    margin-top: 15px;
    justify-content: center; /* Center the items */
}

.special-event {
    background-color: rgba(51, 51, 51, 0.9);
    border-radius: 8px;
    padding: 15px;
    display: flex;
    flex-direction: column; /* Stack content within each event vertically */
    transition: all 0.3s ease;
    width: calc(33.33% - 10px); /* Width for 3 items per row with gap */
    min-width: 200px; /* Minimum width to maintain readability */
    max-width: 100%; /* Ensure it doesn't exceed container on small screens */
    box-sizing: border-box;
    height: 100%; /* Ensure all cards in a row have the same height */
    cursor: pointer;
    border-left: 4px solid var(--title-color, #ff9d4f); /* Add left border with title-color */
}

.special-event:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    background-color: #444;
}

.event-date {
    display: flex;
    width: 100%;
    align-items: flex-start; /* Align items at the top */
}

.event-day {
    background-color: var(--title-color, #ff9d4f); /* Use title-color variable with fallback */
    color: white;
    border-radius: 6px;
    padding: 8px;
    text-align: center;
    min-width: 60px; /* Reduced to fit better in side-by-side layout */
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-shrink: 0; /* Prevent shrinking */
}

.event-day .day {
    font-size: 1.8em;
    font-weight: bold;
    line-height: 1;
}

.event-day .month {
    font-size: 1em;
    text-transform: uppercase;
}

.event-info {
    margin-left: 10px; /* Reduced margin */
    flex-grow: 1;
    overflow: hidden; /* Prevent text overflow */
    display: flex;
    flex-direction: column;
}

.event-info h4 {
    color: var(--title-color, #ff9d4f); /* Use title-color variable with fallback */
    margin: 0 0 5px 0; /* Reduced margin */
    font-size: 1.1em; /* Slightly smaller */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; /* Prevent wrapping for cleaner layout */
}

.event-info p {
    color: #ccc;
    margin: 0;
    line-height: 1.3; /* Reduced line height */
    font-size: 0.9em; /* Slightly smaller */
    display: -webkit-box;
    -webkit-line-clamp: 2; /* Limit to 2 lines */
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.no-special-events {
    text-align: center;
    color: #ccc;
    padding: 20px;
    font-style: italic;
    width: 100%; /* Full width to ensure it centers properly */
    box-sizing: border-box;
}

/* Responsive adjustments for medium screens */
@media screen and (max-width: 992px) {
    .special-event {
        width: calc(50% - 10px); /* 2 per row on medium screens */
    }
    
    .event-info h4 {
        font-size: 1em;
    }
}

@media screen and (max-width: 768px) {
    .special-events-container {
        margin: 0 0 20px 0;
        padding: 15px;
    }
    
    .special-event {
        width: 100%; /* Full width on small screens */
        min-width: 0;
    }
    
    .event-info h4 {
        font-size: 1.1em;
    }
    
    .event-day {
        min-width: 60px;
        padding: 8px;
    }
    
    .event-day .day {
        font-size: 1.5em;
    }
}

/* Fix grid sizing for all screen sizes */
@media screen and (min-width: 769px) {
    .days {
        grid-template-columns: repeat(7, minmax(0, 1fr));
    }
    
    .days div {
        min-height: calc((var(--calendar-width) - (var(--calendar-padding) * 2)) / 7); /* Account for padding */
        max-height: 90px;
        aspect-ratio: 1; /* Make cells square only on larger screens */
    }
}

/* For mobile devices, prioritize consistent layout over square cells */
@media screen and (max-width: 768px) {
    .days div {
        min-height: 40px;
        max-height: 60px;
        aspect-ratio: auto; /* Allow non-square cells on mobile if needed */
    }
}

/* Add specific styles for medium screens */
@media screen and (max-width: 968px) and (min-width: 481px) {
    .calendar {
        padding: calc(var(--calendar-padding) * 0.75);
    }
    
    .days div {
        min-height: calc((var(--calendar-width) - (var(--calendar-padding) * 1.5)) / 7);
    }
}

/* Ensure the calendar is scrollable on very small screens */
@media screen and (max-width: 480px) {
    .calendar {
        padding: var(--calendar-padding-mobile);
        overflow-x: hidden; /* Change from auto to hidden to prevent horizontal scrolling */
    }
    
    .days {
        width: 100%;
    }
    
    .days div {
        min-width: 0; /* Allow cells to shrink as needed */
        min-height: 40px;
        font-size: 0.8rem; /* Smaller font on small screens */
    }
}

/* Event popup styling that needs to be restored */
@media screen and (max-width: 768px) {
    :root {
        --calendar-width: var(--calendar-width-mobile);
        --day-ratio: calc(var(--base-unit) * 2.5);
    }

    .event-popup {
        width: 90vw; /* Full width on smaller screens */
        padding: 20px;
    }

    .event-popup h3 {
        font-size: 1.5em;
    }

    .event-popup .event-item {
        font-size: 1em;
    }
}

@media screen and (max-width: 480px) {
    .event-popup {
        width: 95vw; /* Slightly wider on very small screens */
        padding: 15px;
    }

    .event-popup h3 {
        font-size: 1.3em;
    }

    .event-popup .event-item {
        font-size: 0.9em;
    }
}

/* Add styles for larger screens to prevent calendar from becoming too large */
@media screen and (min-width: 1200px) {
    .calendar-container {
        max-width: 800px; /* Maximum width on very large screens */
        margin: 0 auto;
    }
    
    .calendar {
        max-width: 800px;
    }
    
    .days div {
        max-height: 70px; /* Slightly larger max height for big screens */
    }
}

/* Enhanced styling for highlighted special events */
.special-event.highlighted-event {
    border-left: 4px solid var(--title-color, #c3a633);
    box-shadow: 0 3px 10px rgba(195, 166, 51, 0.3);
    background-color: rgba(51, 51, 51, 0.95);
}

.special-event.highlighted-event:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 15px rgba(195, 166, 51, 0.4);
    background-color: #444;
}

.event-special-tag {
    font-size: 0.8em;
    color: var(--title-color, #c3a633);
    font-weight: bold;
    margin-top: 4px;
    display: inline-block;
    border: 1px solid var(--title-color, #c3a633);
    padding: 2px 6px;
    border-radius: 4px;
    background-color: rgba(195, 166, 51, 0.1);
}

/* For consistency, also update the event-recurrence style */
.event-recurrence {
    font-size: 0.8em;
    margin-bottom: 3px;
    color: rgba(255, 255, 255, 0.7);
    display: inline-block;
}