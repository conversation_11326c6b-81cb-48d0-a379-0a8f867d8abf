/* Donation Page Styles */

.donation-section {
    padding: 60px 0;
    background-color: #f9f9f9;
}

.donation-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
}

.donation-intro h2 {
    color: #4F7942;
    margin-bottom: 20px;
}

.donation-options {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    justify-content: center;
}

.donation-form-container {
    flex: 1;
    min-width: 300px;
    max-width: 600px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

.donation-info {
    flex: 1;
    min-width: 300px;
    max-width: 400px;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.amount-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.amount-option {
    background-color: #f0f0f0;
    border: 2px solid #ddd;
    border-radius: 4px;
    padding: 10px 15px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.amount-option:hover {
    background-color: #e8e8e8;
}

.amount-option.active {
    background-color: #4F7942;
    color: white;
    border-color: #4F7942;
}

.custom-amount-input {
    margin-top: 10px;
}

.custom-amount-input input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

input[type="text"],
input[type="email"],
input[type="number"],
select,
textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.2s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus {
    border-color: #4F7942;
    outline: none;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkbox-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.payment-element-container {
    margin-top: 30px;
}

.donate-button {
    background-color: #4F7942;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 14px 24px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    transition: background-color 0.2s ease;
    position: relative;
}

.donate-button:hover {
    background-color: #3e6134;
}

.donate-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    position: absolute;
    top: calc(50% - 10px);
    left: calc(50% - 10px);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.hidden {
    display: none !important;
}

.payment-message {
    color: #df1b41;
    text-align: center;
    margin-top: 15px;
    font-size: 14px;
}

.donation-method {
    margin-bottom: 30px;
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.donation-method h4 {
    color: #4F7942;
    margin-bottom: 10px;
}

.donation-success {
    padding: 80px 0;
    text-align: center;
}

.success-message {
    max-width: 600px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 8px;
    padding: 40px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.success-message h2 {
    color: #4F7942;
    margin-bottom: 20px;
}

.success-actions {
    margin-top: 30px;
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.button {
    display: inline-block;
    background-color: #4F7942;
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 4px;
    font-weight: 600;
    transition: background-color 0.2s ease;
}

.button:hover {
    background-color: #3e6134;
}

.button.secondary {
    background-color: #f0f0f0;
    color: #333;
}

.button.secondary:hover {
    background-color: #e0e0e0;
}

/* Responsive styles */
@media (max-width: 768px) {
    .donation-options {
        flex-direction: column;
    }
    
    .donation-form-container,
    .donation-info {
        max-width: 100%;
    }
    
    .amount-options {
        justify-content: center;
    }
}
