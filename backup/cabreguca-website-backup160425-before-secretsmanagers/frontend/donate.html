<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Donate to CabUCA</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/donation.css">
    <script src="https://js.stripe.com/v3/"></script>
</head>
<body>
    <header class="home-title">
        Support Our Ministry
    </header>
    
    <div class="content-container">
        <header class="home-header">
            <h1>Make a Donation</h1>
        </header>
        <section class="home-text-alt">
            <p>Your generous donations help us continue our work in the community. All contributions are greatly appreciated and will be used to support our various ministries and outreach programs.</p>
        </section>
    </div>

    <div class="content-container">
        <div class="donation-form-container">
            <form id="donation-form">
                <div class="form-group">
                    <label for="donation-amount">Donation Amount (AUD)</label>
                    <div class="amount-options">
                        <button type="button" class="amount-option" data-amount="1000">$10</button>
                        <button type="button" class="amount-option" data-amount="2500">$25</button>
                        <button type="button" class="amount-option" data-amount="5000">$50</button>
                        <button type="button" class="amount-option" data-amount="10000">$100</button>
                        <button type="button" class="amount-option custom-amount">Custom</button>
                    </div>
                    <div class="custom-amount-input" style="display: none;">
                        <input type="number" id="custom-amount" min="1" step="0.01" placeholder="Enter amount">
                    </div>
                    <input type="hidden" id="donation-amount" name="amount" required>
                </div>

                <div class="form-group">
                    <label for="donation-purpose">Purpose (Optional)</label>
                    <select id="donation-purpose" name="purpose">
                        <option value="General">General Donation</option>
                        <option value="Building Fund">Building Fund</option>
                        <option value="Community Outreach">Community Outreach</option>
                        <option value="Youth Ministry">Youth Ministry</option>
                        <option value="Mission Work">Mission Work</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="anonymous-donation" name="anonymous">
                        <label for="anonymous-donation">Make this donation anonymous</label>
                    </div>
                </div>

                <div id="donor-info" class="donor-info">
                    <div class="form-group">
                        <label for="donor-name">Your Name</label>
                        <input type="text" id="donor-name" name="name">
                    </div>

                    <div class="form-group">
                        <label for="donor-email">Email Address</label>
                        <input type="email" id="donor-email" name="email">
                    </div>

                    <div class="form-group">
                        <label for="donor-message">Message (Optional)</label>
                        <textarea id="donor-message" name="message" rows="3"></textarea>
                    </div>
                </div>

                <div class="form-group payment-element-container">
                    <label>Payment Information</label>
                    <div id="payment-element"></div>
                </div>

                <div class="form-group">
                    <button type="submit" id="submit-button" class="donate-button">
                        <span id="button-text">Donate Now</span>
                        <div id="spinner" class="spinner hidden"></div>
                    </button>
                </div>

                <div id="payment-message" class="payment-message hidden"></div>
            </form>
        </div>
    </div>

    <div class="content-container">
        <header class="home-header">
            <h1>Other Ways to Give</h1>
        </header>
        <section class="home-text-alt">
            <div class="donation-method">
                <h3>Direct Bank Transfer</h3>
                <p>You can make a direct deposit to our church account:</p>
                <p><strong>Account Name:</strong> Cabramatta Uniting Church</p>
                <p><strong>BSB:</strong> 000-000</p>
                <p><strong>Account Number:</strong> ********</p>
                <p><strong>Reference:</strong> Please include "Donation" and your name</p>
            </div>

            <div class="donation-method">
                <h3>In Person</h3>
                <p>You can also give during our Sunday services or drop by the church office during business hours.</p>
            </div>

            <div class="donation-method">
                <h3>Tax Deductibility</h3>
                <p>Please note that donations to the church are not tax-deductible. However, donations specifically to our registered charitable programs may be tax-deductible. Please contact us for more information.</p>
            </div>
        </section>
    </div>

    <div class="donation-success hidden" id="donation-success">
        <div class="content-container">
            <header class="home-header">
                <h1>Thank You for Your Donation!</h1>
            </header>
            <section class="home-text-alt">
                <p>Your generous contribution helps us continue our mission in the community.</p>
                <p>A receipt has been sent to your email address.</p>
                <a href="#" data-page="home" class="button-link">Return to Homepage</a>
            </section>
        </div>
    </div>

    <script src="js/api-config.js"></script>
    <script src="js/donation.js"></script>
</body>
</html>
