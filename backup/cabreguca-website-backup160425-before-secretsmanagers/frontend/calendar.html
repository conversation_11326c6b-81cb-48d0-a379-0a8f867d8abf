<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>CabUCA Calendar</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components/calendar.css">
    <link rel="stylesheet" href="css/components/main.css">
</head>
<body>

    <header class="home-title">
      Events
    </header>
    
    <!-- Main container for layout -->
    <div class="main-layout-container">
        <!-- Left column: Special events + Calendar -->
        <div class="left-column">
            <!-- Special events first -->
            <div class="special-events-container">
                <h3>Special Events</h3>
                <div class="special-events-list">
                    <!-- Special events will be populated by JavaScript -->
                </div>
            </div>
            
            <!-- Calendar second -->
            <div class="calendar-container">
                <button class="refresh-button" id="refresh">Refresh & Return</button>
                <div class="calendar">
                    <div class="calendar-header">
                        <button id="prev">&#10094;</button>
                        <div id="month-year"></div>
                        <button id="next">&#10095;</button>
                    </div>
                    <div class="weekdays">
                        <div>Sun</div>
                        <div>Mon</div>
                        <div>Tue</div>
                        <div>Wed</div>
                        <div>Thu</div>
                        <div>Fri</div>
                        <div>Sat</div>
                    </div>
                    <div class="days" id="days"></div>
                </div>
            </div>
        </div>
        
        <!-- Right column: Events sidebar -->
        <div class="right-column">
            <div class="events-side-pane">
                <h3>Events This Month</h3>
                <div class="month-events-list"></div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner while events are being fetched -->
    <div class="loading-spinner" id="loading-spinner">
        <div class="spinner"></div>
        <p>Loading events...</p>
    </div>

    <div class="overlay"></div>
    <div class="event-popup">
        <button class="event-popup-close">&times;</button>
        <h3>Events</h3>
        <div class="event-list"></div>
    </div>

    <script src="js/calendar.js"></script>
</body>
</html>