# Secrets Management for CabUCA Church Website

This document outlines the approach for managing secrets and configuration values in the CabUCA Church Website project.

## Overview

We've implemented a dual-approach to secrets management:

1. **Development Environment**: .NET User Secrets
2. **Production Environment**: AWS Parameter Store

This approach provides a secure, scalable, and cost-effective solution for managing sensitive configuration values like API keys, connection strings, and credentials.

## Development Environment: .NET User Secrets

### What is .NET User Secrets?

User Secrets is a secure way to store private application settings outside your project tree. The secrets are stored in a JSON file in a user-specific directory on the developer's machine, separate from the source code.

### Location of User Secrets

- **Windows**: `%APPDATA%\Microsoft\UserSecrets\<user_secrets_id>\secrets.json`
- **macOS/Linux**: `~/.microsoft/usersecrets/<user_secrets_id>/secrets.json`

### Managing User Secrets

#### Viewing Current Secrets

```bash
dotnet user-secrets list --project <path-to-project>
```

#### Setting a Secret

```bash
dotnet user-secrets set "StripeSettings:SecretKey" "your-secret-key" --project <path-to-project>
```

#### Removing a Secret

```bash
dotnet user-secrets remove "StripeSettings:SecretKey" --project <path-to-project>
```

#### Clearing All Secrets

```bash
dotnet user-secrets clear --project <path-to-project>
```

### Required Secrets for Development

The following secrets should be set for local development:

```bash
dotnet user-secrets set "StripeSettings:SecretKey" "sk_test_your_test_key" --project <path-to-project>
dotnet user-secrets set "StripeSettings:PublishableKey" "pk_test_your_test_key" --project <path-to-project>
dotnet user-secrets set "StripeSettings:WebhookSecret" "whsec_your_webhook_secret" --project <path-to-project>
dotnet user-secrets set "JwtSettings:Secret" "your_jwt_secret" --project <path-to-project>
dotnet user-secrets set "AdminSettings:Username" "admin" --project <path-to-project>
dotnet user-secrets set "AdminSettings:Password" "your_admin_password" --project <path-to-project>
dotnet user-secrets set "AdminSettings:Email" "<EMAIL>" --project <path-to-project>
```

## Production Environment: AWS Parameter Store

### What is AWS Parameter Store?

AWS Parameter Store is a secure, hierarchical storage service provided by AWS Systems Manager. It allows you to store configuration data and secrets as parameter values, with optional encryption using AWS KMS.

### Parameter Hierarchy

We use a hierarchical structure for our parameters:

```
/CabUCA/
  ├── StripeSettings/
  │   ├── SecretKey
  │   ├── PublishableKey
  │   └── WebhookSecret
  ├── JwtSettings/
  │   └── Secret
  └── AdminSettings/
      ├── Username
      ├── Password
      └── Email
```

### Managing Parameters in AWS

#### Creating Parameters

```bash
# String parameter (not encrypted)
aws ssm put-parameter --name "/CabUCA/StripeSettings/PublishableKey" --value "pk_live_your_key" --type String

# Secure string parameter (encrypted)
aws ssm put-parameter --name "/CabUCA/StripeSettings/SecretKey" --value "sk_live_your_key" --type SecureString
```

#### Updating Parameters

```bash
aws ssm put-parameter --name "/CabUCA/StripeSettings/SecretKey" --value "sk_live_your_new_key" --type SecureString --overwrite
```

#### Viewing Parameters

```bash
# List all parameters in the hierarchy
aws ssm get-parameters-by-path --path "/CabUCA/" --recursive

# Get a specific parameter
aws ssm get-parameter --name "/CabUCA/StripeSettings/PublishableKey"

# Get a specific secure parameter with decryption
aws ssm get-parameter --name "/CabUCA/StripeSettings/SecretKey" --with-decryption
```

### Required Parameters for Production

The following parameters should be set in AWS Parameter Store for production:

```bash
# Stripe Settings
aws ssm put-parameter --name "/CabUCA/StripeSettings/SecretKey" --value "sk_live_your_key" --type SecureString
aws ssm put-parameter --name "/CabUCA/StripeSettings/PublishableKey" --value "pk_live_your_key" --type String
aws ssm put-parameter --name "/CabUCA/StripeSettings/WebhookSecret" --value "whsec_your_webhook_secret" --type SecureString

# JWT Settings
aws ssm put-parameter --name "/CabUCA/JwtSettings/Secret" --value "your_production_jwt_secret" --type SecureString

# Admin Settings
aws ssm put-parameter --name "/CabUCA/AdminSettings/Username" --value "admin" --type String
aws ssm put-parameter --name "/CabUCA/AdminSettings/Password" --value "your_secure_admin_password" --type SecureString
aws ssm put-parameter --name "/CabUCA/AdminSettings/Email" --value "<EMAIL>" --type String
```

## IAM Permissions

### EC2 Instance Role

For the EC2 instance running the application, create an IAM role with the following permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ssm:GetParameter",
                "ssm:GetParameters",
                "ssm:GetParametersByPath"
            ],
            "Resource": "arn:aws:ssm:*:*:parameter/CabUCA/*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "kms:Decrypt"
            ],
            "Resource": "arn:aws:kms:*:*:key/your-kms-key-id"
        }
    ]
}
```

## Implementation Details

### Configuration in Program.cs

The application is configured to use User Secrets in development and AWS Parameter Store in production:

```csharp
// Add User Secrets in Development, AWS Parameter Store in Production
if (builder.Environment.IsDevelopment())
{
    // User Secrets are automatically loaded in Development
    builder.Configuration.AddUserSecrets<Program>();
    Console.WriteLine("Using User Secrets for configuration in Development");
}
else
{
    // Add AWS Parameter Store in non-development environments
    builder.Configuration.AddSystemsManager("/CabUCA/", optional: true, reloadAfter: TimeSpan.FromMinutes(5));
    Console.WriteLine("Using AWS Parameter Store for configuration in Production");
}

// Always add environment variables as the final override
builder.Configuration.AddEnvironmentVariables();
```

### Accessing Configuration Values

Configuration values can be accessed using the standard .NET Configuration API:

```csharp
// Direct access
var stripeSecretKey = builder.Configuration["StripeSettings:SecretKey"];

// Strongly typed configuration
builder.Services.Configure<StripeSettings>(builder.Configuration.GetSection("StripeSettings"));
```

## Benefits of This Approach

1. **Security**: Secrets are never stored in source code or configuration files
2. **Separation of Concerns**: Development and production secrets are managed separately
3. **Cost-Effective**: AWS Parameter Store is free for standard parameters
4. **Scalability**: Easy to add new parameters as the application grows
5. **Auditability**: AWS Parameter Store provides history and audit trails
6. **Integration**: Seamless integration with AWS infrastructure

## Migrating from .env Files

We've migrated from using .env files to this more secure approach. If you previously used .env files, you should:

1. Move all secrets to User Secrets for local development
2. Add all production secrets to AWS Parameter Store
3. Remove any .env files containing secrets

## Troubleshooting

### Common Issues

1. **Missing Secrets**: If you see errors about missing configuration values, ensure you've set all required secrets.
2. **AWS Permissions**: Ensure your EC2 instance has the correct IAM role with appropriate permissions.
3. **Parameter Path**: Verify that the parameter path in AWS Parameter Store matches the path in your code.

### Debugging

To debug configuration issues:

1. In development, list your user secrets: `dotnet user-secrets list --project <path-to-project>`
2. In production, check the application logs for AWS Parameter Store errors
3. Verify AWS credentials and permissions using the AWS CLI

## References

- [.NET User Secrets Documentation](https://learn.microsoft.com/en-us/aspnet/core/security/app-secrets)
- [AWS Parameter Store Documentation](https://docs.aws.amazon.com/systems-manager/latest/userguide/systems-manager-parameter-store.html)
- [Amazon.Extensions.Configuration.SystemsManager](https://github.com/aws/aws-dotnet-extensions-configuration)
