#!/bin/bash

# Script to set up AWS Parameter Store for CabUCA Church Website
# This script helps you set up the required parameters in AWS Parameter Store for production

# Set your AWS region
AWS_REGION="ap-southeast-2"  # Change this to your AWS region

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}CabUCA Church Website - AWS Parameter Store Setup${NC}"
echo "======================================================="
echo ""
echo -e "${YELLOW}This script will help you set up the required parameters in AWS Parameter Store for production.${NC}"
echo -e "${YELLOW}Make sure you have the AWS CLI installed and configured with appropriate permissions.${NC}"
echo ""

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo -e "${RED}Error: AWS CLI is not installed. Please install it first.${NC}"
    echo "Visit: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
    exit 1
fi

# Check if AWS CLI is configured
if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}Error: AWS CLI is not configured. Please run 'aws configure' first.${NC}"
    exit 1
fi

echo -e "${GREEN}AWS CLI is installed and configured.${NC}"
echo ""

# Function to prompt for a parameter value
prompt_for_parameter() {
    local param_name=$1
    local param_description=$2
    local is_secret=$3
    local default_value=$4
    
    if [ -n "$default_value" ]; then
        read -p "$param_description [$default_value]: " param_value
        param_value=${param_value:-$default_value}
    else
        read -p "$param_description: " param_value
    fi
    
    if [ -z "$param_value" ]; then
        echo -e "${RED}Error: Parameter value cannot be empty.${NC}"
        prompt_for_parameter "$param_name" "$param_description" "$is_secret" "$default_value"
    else
        if [ "$is_secret" = true ]; then
            aws ssm put-parameter --name "/CabUCA/$param_name" --value "$param_value" --type SecureString --region $AWS_REGION --overwrite
        else
            aws ssm put-parameter --name "/CabUCA/$param_name" --value "$param_value" --type String --region $AWS_REGION --overwrite
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}Parameter /CabUCA/$param_name created successfully.${NC}"
        else
            echo -e "${RED}Error creating parameter /CabUCA/$param_name.${NC}"
        fi
    fi
}

echo -e "${BLUE}Setting up Stripe parameters...${NC}"
prompt_for_parameter "StripeSettings/SecretKey" "Enter your Stripe Secret Key (sk_live_...)" true
prompt_for_parameter "StripeSettings/PublishableKey" "Enter your Stripe Publishable Key (pk_live_...)" false
prompt_for_parameter "StripeSettings/WebhookSecret" "Enter your Stripe Webhook Secret (whsec_...)" true

echo -e "${BLUE}Setting up JWT parameters...${NC}"
prompt_for_parameter "JwtSettings/Secret" "Enter your JWT Secret (should be a long, random string)" true

echo -e "${BLUE}Setting up Admin parameters...${NC}"
prompt_for_parameter "AdminSettings/Username" "Enter the admin username" false "admin"
prompt_for_parameter "AdminSettings/Password" "Enter the admin password" true
prompt_for_parameter "AdminSettings/Email" "Enter the admin email" false "<EMAIL>"

echo ""
echo -e "${GREEN}All parameters have been set up successfully in AWS Parameter Store.${NC}"
echo -e "${YELLOW}Path: /CabUCA/${NC}"
echo ""
echo "To verify the parameters, run:"
echo "aws ssm get-parameters-by-path --path \"/CabUCA/\" --recursive --region $AWS_REGION"
echo ""
echo "To view secure parameters with decryption, run:"
echo "aws ssm get-parameters-by-path --path \"/CabUCA/\" --recursive --with-decryption --region $AWS_REGION"
echo ""
echo -e "${BLUE}Done!${NC}"
