// Stripe API configuration
let stripe;
let elements;
let paymentElement;
let clientSecret;

// DOM Elements
const donationForm = document.getElementById('donation-form');
const submitButton = document.getElementById('submit-button');
const paymentMessage = document.getElementById('payment-message');
const buttonText = document.getElementById('button-text');
const spinner = document.getElementById('spinner');

// Donation details
let selectedPurpose = 'General';
let selectedAmount = 0;

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    // Set up purpose selection
    const purposeButtons = document.querySelectorAll('.purpose-btn');
    purposeButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons
            purposeButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            button.classList.add('active');
            // Update selected purpose
            selectedPurpose = button.getAttribute('data-purpose');
            document.getElementById('donation-purpose').value = selectedPurpose;
        });
    });

    // Set up amount selection
    const amountButtons = document.querySelectorAll('.amount-btn');
    const customAmountContainer = document.querySelector('.custom-amount-container');
    const customAmountInput = document.getElementById('custom-amount-input');

    amountButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons
            amountButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            button.classList.add('active');
            
            const amountType = button.getAttribute('data-amount');
            
            if (amountType === 'custom') {
                // Show custom amount input
                customAmountContainer.style.display = 'block';
                customAmountInput.focus();
                selectedAmount = 0;
            } else {
                // Hide custom amount input
                customAmountContainer.style.display = 'none';
                // Update selected amount
                selectedAmount = parseInt(amountType);
                document.getElementById('donation-amount').value = selectedAmount;
                
                // Enable submit button if amount is valid
                if (selectedAmount > 0) {
                    submitButton.disabled = false;
                    initializeStripe();
                }
            }
        });
    });

    // Handle custom amount input
    customAmountInput.addEventListener('input', () => {
        const amount = parseInt(customAmountInput.value);
        if (amount > 0) {
            selectedAmount = amount;
            document.getElementById('donation-amount').value = selectedAmount;
            submitButton.disabled = false;
            initializeStripe();
        } else {
            submitButton.disabled = true;
        }
    });

    // Handle anonymous checkbox
    const anonymousCheckbox = document.getElementById('anonymous');
    anonymousCheckbox.addEventListener('change', () => {
        const nameInput = document.getElementById('donor-name');
        const emailInput = document.getElementById('donor-email');
        const phoneInput = document.getElementById('donor-phone');
        
        if (anonymousCheckbox.checked) {
            nameInput.disabled = true;
            emailInput.disabled = true;
            phoneInput.disabled = true;
            
            // Save current values
            nameInput.dataset.value = nameInput.value;
            emailInput.dataset.value = emailInput.value;
            phoneInput.dataset.value = phoneInput.value;
            
            // Clear inputs
            nameInput.value = '';
            emailInput.value = '';
            phoneInput.value = '';
        } else {
            nameInput.disabled = false;
            emailInput.disabled = false;
            phoneInput.disabled = false;
            
            // Restore values if they were saved
            if (nameInput.dataset.value) nameInput.value = nameInput.dataset.value;
            if (emailInput.dataset.value) emailInput.value = emailInput.dataset.value;
            if (phoneInput.dataset.value) phoneInput.value = phoneInput.dataset.value;
        }
    });

    // Handle form submission
    donationForm.addEventListener('submit', handleSubmit);
});

// Initialize Stripe
async function initializeStripe() {
    if (!selectedAmount || selectedAmount <= 0) return;
    
    try {
        // Clear any existing payment elements
        const paymentElementContainer = document.getElementById('payment-element');
        paymentElementContainer.innerHTML = '';
        
        // Show loading state
        setLoading(true);
        
        // Create a donation record and get a payment intent
        const response = await fetch('/api/donations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                amount: selectedAmount,
                purpose: selectedPurpose,
                donorName: document.getElementById('donor-name').value,
                donorEmail: document.getElementById('donor-email').value,
                donorPhone: document.getElementById('donor-phone').value,
                isAnonymous: document.getElementById('anonymous').checked,
                message: document.getElementById('message').value
            }),
        });
        
        if (!response.ok) {
            throw new Error('Failed to create payment intent');
        }
        
        const data = await response.json();
        clientSecret = data.clientSecret;
        
        // Initialize Stripe
        stripe = Stripe('pk_test_your_test_key'); // Replace with your Stripe publishable key
        
        // Create payment element options
        const options = {
            clientSecret: clientSecret,
            appearance: {
                theme: 'stripe',
                variables: {
                    colorPrimary: '#ff5100',
                    colorBackground: '#ffffff',
                    colorText: '#333333',
                    colorDanger: '#e25950',
                    fontFamily: 'Arial, sans-serif',
                    borderRadius: '4px',
                },
            },
        };
        
        // Create Elements instance
        elements = stripe.elements(options);
        
        // Create and mount the Payment Element
        paymentElement = elements.create('payment');
        paymentElement.mount('#payment-element');
        
        // Hide loading state
        setLoading(false);
    } catch (error) {
        console.error('Error initializing Stripe:', error);
        showMessage('An error occurred while setting up the payment form. Please try again later.');
        setLoading(false);
    }
}

// Handle form submission
async function handleSubmit(e) {
    e.preventDefault();
    
    if (!stripe || !elements || !clientSecret) {
        return;
    }
    
    setLoading(true);
    
    try {
        const { error } = await stripe.confirmPayment({
            elements,
            confirmParams: {
                return_url: `${window.location.origin}/donation-confirmation.html`,
                receipt_email: document.getElementById('donor-email').value || undefined,
            },
        });
        
        if (error) {
            if (error.type === 'card_error' || error.type === 'validation_error') {
                showMessage(error.message);
            } else {
                showMessage('An unexpected error occurred.');
            }
        }
    } catch (error) {
        console.error('Payment confirmation error:', error);
        showMessage('An error occurred while processing your payment. Please try again later.');
    } finally {
        setLoading(false);
    }
}

// Helper function to show a message
function showMessage(messageText) {
    paymentMessage.textContent = messageText;
    paymentMessage.classList.remove('hidden');
    
    setTimeout(() => {
        paymentMessage.classList.add('hidden');
        paymentMessage.textContent = '';
    }, 8000);
}

// Helper function to set loading state
function setLoading(isLoading) {
    if (isLoading) {
        submitButton.disabled = true;
        spinner.classList.remove('hidden');
        buttonText.classList.add('hidden');
    } else {
        submitButton.disabled = false;
        spinner.classList.add('hidden');
        buttonText.classList.remove('hidden');
    }
}
