/* Donation Page Styles */

.donation-intro {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

.donation-intro h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.donation-intro p {
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

.donation-options {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.donation-purpose-section,
.donation-amount-section {
    width: 100%;
}

.donation-purpose-section h3,
.donation-amount-section h3 {
    color: var(--primary-dark);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    text-align: center;
}

.purpose-options,
.amount-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;
    justify-content: center;
}

.purpose-btn,
.amount-btn {
    padding: 0.8rem 1.5rem;
    background-color: #f5f5f5;
    border: 2px solid #ddd;
    border-radius: 30px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #555;
    font-weight: 500;
}

.purpose-btn:hover,
.amount-btn:hover {
    background-color: #e9e9e9;
    border-color: #ccc;
}

.purpose-btn.active,
.amount-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.custom-amount-container {
    margin-top: 1rem;
    text-align: center;
}

.custom-amount-container label {
    display: block;
    margin-bottom: 0.5rem;
    color: #555;
}

#custom-amount-input {
    padding: 0.8rem;
    border: 2px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    width: 150px;
    text-align: center;
}

.donation-form {
    background-color: rgba(255, 255, 255, 0.9);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.donation-form h3 {
    color: var(--primary-dark);
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
    text-align: center;
}

.optional-note {
    text-align: center;
    color: #777;
    margin-bottom: 1.5rem;
    font-style: italic;
    font-size: 0.9rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #555;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="tel"],
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form-group small {
    display: block;
    color: #777;
    margin-top: 0.3rem;
    font-size: 0.85rem;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.checkbox-group label {
    margin-bottom: 0;
    cursor: pointer;
}

.form-actions {
    margin-top: 2rem;
    text-align: center;
}

.donate-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 30px;
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
    position: relative;
    min-width: 180px;
}

.donate-button:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.donate-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    position: absolute;
    right: 10px;
    top: calc(50% - 10px);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.hidden {
    display: none;
}

.payment-message {
    color: #e25950;
    text-align: center;
    margin-top: 1rem;
    padding: 0.8rem;
    background-color: rgba(226, 89, 80, 0.1);
    border-radius: 4px;
}

.payment-message.success {
    color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

#payment-element {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
}

.donation-info {
    background-color: rgba(255, 255, 255, 0.9);
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.donation-info h3 {
    color: var(--primary-dark);
    margin-bottom: 0.8rem;
    font-size: 1.2rem;
}

.donation-info p {
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.donation-info a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.donation-info a:hover {
    text-decoration: underline;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
    .donation-options {
        padding: 1rem;
    }
    
    .purpose-btn,
    .amount-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
    
    .donation-form {
        padding: 1.5rem;
    }
    
    .donate-button {
        width: 100%;
    }
}

@media screen and (min-width: 768px) {
    .donation-options {
        flex-direction: row;
        align-items: flex-start;
    }
    
    .donation-purpose-section,
    .donation-amount-section {
        width: 50%;
    }
}
