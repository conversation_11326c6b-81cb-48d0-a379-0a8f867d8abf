<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Donate - CRUCA</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components/main.css">
    <link rel="stylesheet" href="css/components/donate.css">
</head>
<body>
    <header class="home-title">
        Support Our Ministry
    </header>
    <div class="content-container">
        <section class="donation-intro">
            <h2>Your Generosity Makes a Difference</h2>
            <p>Thank you for considering a donation to Caboolture Region Uniting Church. Your support helps us continue our mission and serve our community through various programs and ministries.</p>
        </section>

        <div class="donation-options">
            <div class="donation-purpose-section">
                <h3>Select a Purpose</h3>
                <div class="purpose-options">
                    <button class="purpose-btn active" data-purpose="General">General Fund</button>
                    <button class="purpose-btn" data-purpose="Building">Building Fund</button>
                    <button class="purpose-btn" data-purpose="Mission">Mission & Outreach</button>
                    <button class="purpose-btn" data-purpose="Youth">Youth Ministry</button>
                </div>
            </div>

            <div class="donation-amount-section">
                <h3>Select an Amount</h3>
                <div class="amount-options">
                    <button class="amount-btn" data-amount="10">$10</button>
                    <button class="amount-btn" data-amount="25">$25</button>
                    <button class="amount-btn" data-amount="50">$50</button>
                    <button class="amount-btn" data-amount="100">$100</button>
                    <button class="amount-btn" data-amount="250">$250</button>
                    <button class="amount-btn custom-amount" data-amount="custom">Custom</button>
                </div>
                <div class="custom-amount-container" style="display: none;">
                    <label for="custom-amount-input">Enter Amount ($):</label>
                    <input type="number" id="custom-amount-input" min="1" step="1" placeholder="Enter amount">
                </div>
            </div>
        </div>

        <form id="donation-form" class="donation-form">
            <h3>Your Information</h3>
            <p class="optional-note">All personal information is optional. You may donate anonymously.</p>
            
            <div class="form-group">
                <label for="donor-name">Name (Optional)</label>
                <input type="text" id="donor-name" name="donorName" placeholder="Your name">
            </div>
            
            <div class="form-group">
                <label for="donor-email">Email (Optional)</label>
                <input type="email" id="donor-email" name="donorEmail" placeholder="Your email">
                <small>We'll send your receipt to this email if provided.</small>
            </div>
            
            <div class="form-group">
                <label for="donor-phone">Phone (Optional)</label>
                <input type="tel" id="donor-phone" name="donorPhone" placeholder="Your phone number">
            </div>
            
            <div class="form-group">
                <label for="message">Message (Optional)</label>
                <textarea id="message" name="message" rows="3" placeholder="Add a message with your donation"></textarea>
            </div>
            
            <div class="form-group checkbox-group">
                <input type="checkbox" id="anonymous" name="isAnonymous">
                <label for="anonymous">Make this donation anonymous</label>
            </div>

            <div class="form-group">
                <input type="hidden" id="donation-purpose" name="purpose" value="General">
                <input type="hidden" id="donation-amount" name="amount" value="0">
            </div>
            
            <div id="payment-element">
                <!-- Stripe Elements will be inserted here -->
            </div>
            
            <div class="form-actions">
                <button type="submit" id="submit-button" class="donate-button" disabled>
                    <span id="button-text">Donate</span>
                    <div id="spinner" class="spinner hidden"></div>
                </button>
            </div>
            
            <div id="payment-message" class="payment-message hidden"></div>
        </form>

        <section class="donation-info">
            <h3>Other Ways to Give</h3>
            <p>If you prefer to donate via bank transfer or in person, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a> or call us at 5499 3411.</p>
            
            <h3>Tax Information</h3>
            <p>Donations to Caboolture Region Uniting Church are tax-deductible. You will receive a receipt for your donation that you can use for tax purposes.</p>
        </section>
    </div>

    <script src="https://js.stripe.com/v3/"></script>
    <script src="js/donate.js"></script>
</body>
</html>
