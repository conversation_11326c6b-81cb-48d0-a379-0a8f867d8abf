using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace CabUCA.API.Models
{
    public class Transaction
    {
        [Key]
        public int Id { get; set; }
        
        // Foreign key to Donation
        public int DonationId { get; set; }
        
        // Navigation property to Donation
        [JsonIgnore]
        public Donation Donation { get; set; }
        
        // Stripe payment intent ID
        [Required]
        public string PaymentIntentId { get; set; }
        
        // Transaction status (e.g., "pending", "completed", "failed")
        [Required]
        public string Status { get; set; }
        
        // Payment method (e.g., "card", "bank_transfer")
        public string PaymentMethod { get; set; }
        
        // Transaction date
        public DateTime TransactionDate { get; set; }
        
        // Last four digits of the card (if applicable)
        public string? LastFour { get; set; }
        
        // Card brand (if applicable)
        public string? CardBrand { get; set; }
        
        // Error message (if any)
        public string? ErrorMessage { get; set; }
        
        // Receipt URL from Stripe
        public string? ReceiptUrl { get; set; }
    }
}
