using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace CabUCA.API.Models
{
    public class Donation
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public decimal Amount { get; set; }
        
        [Required]
        public DateTime DonationDate { get; set; }
        
        // Purpose of the donation (e.g., "General", "Building Fund", "Mission", etc.)
        public string Purpose { get; set; } = "General";
        
        // Optional donor information - can be anonymous
        public string? DonorName { get; set; }
        public string? DonorEmail { get; set; }
        public string? DonorPhone { get; set; }
        
        // Whether the donation is anonymous
        public bool IsAnonymous { get; set; }
        
        // Optional message from the donor
        public string? Message { get; set; }
        
        // Optional reference to an event if the donation is for a specific event
        public int? EventId { get; set; }
        
        [JsonIgnore]
        public Event? Event { get; set; }
        
        // Navigation property to Transaction
        public Transaction? Transaction { get; set; }
        
        // Admin tracking fields
        public string? ProcessedBy { get; set; }
        public DateTime? ProcessedDate { get; set; }
        
        // Notes for administrators
        public string? AdminNotes { get; set; }
    }
}
