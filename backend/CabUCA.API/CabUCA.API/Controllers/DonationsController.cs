using CabUCA.API.Dtos.Donations;
using CabUCA.API.Interfaces;
using CabUCA.API.Mappers;
using CabUCA.API.Models;
using CabUCA.API.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Stripe;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace CabUCA.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DonationsController : ControllerBase
    {
        private readonly IDonationRepository _donationRepository;
        private readonly StripeService _stripeService;
        private readonly ILogger<DonationsController> _logger;

        public DonationsController(
            IDonationRepository donationRepository,
            StripeService stripeService,
            ILogger<DonationsController> logger)
        {
            _donationRepository = donationRepository;
            _stripeService = stripeService;
            _logger = logger;
        }

        // GET: api/Donations
        [HttpGet]
        [Authorize(Roles = "Admin,ExecutiveAdmin")]
        public async Task<ActionResult<IEnumerable<DonationDto>>> GetDonations()
        {
            var donations = await _donationRepository.GetAllDonationsAsync();
            return Ok(donations.Select(d => d.ToDto()));
        }

        // GET: api/Donations/5
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin,ExecutiveAdmin")]
        public async Task<ActionResult<DonationDto>> GetDonation(int id)
        {
            var donation = await _donationRepository.GetDonationByIdAsync(id);

            if (donation == null)
            {
                return NotFound();
            }

            return Ok(donation.ToDto());
        }

        // GET: api/Donations/Summary
        [HttpGet("summary")]
        [Authorize(Roles = "Admin,ExecutiveAdmin")]
        public async Task<ActionResult<IEnumerable<DonationSummaryDto>>> GetDonationsSummary()
        {
            var donations = await _donationRepository.GetAllDonationsAsync();
            return Ok(donations.Select(d => d.ToSummaryDto()));
        }

        // GET: api/Donations/Event/5
        [HttpGet("event/{eventId}")]
        [Authorize(Roles = "Admin,ExecutiveAdmin")]
        public async Task<ActionResult<IEnumerable<DonationDto>>> GetDonationsByEvent(int eventId)
        {
            var donations = await _donationRepository.GetDonationsByEventIdAsync(eventId);
            return Ok(donations.Select(d => d.ToDto()));
        }

        // POST: api/Donations
        [HttpPost]
        [AllowAnonymous]
        public async Task<ActionResult<DonationDto>> CreateDonation(CreateDonationDto createDonationDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                // Create a donation record
                var donation = createDonationDto.ToEntity();
                await _donationRepository.CreateDonationAsync(donation);

                // Create a payment intent with Stripe
                var paymentIntent = await _stripeService.CreatePaymentIntentAsync(
                    donation.Amount,
                    "aud",
                    $"Donation - {donation.Purpose}"
                );

                // Create a transaction record
                var transaction = new Transaction
                {
                    DonationId = donation.Id,
                    PaymentIntentId = paymentIntent.Id,
                    Status = paymentIntent.Status,
                    PaymentMethod = "card", // Default to card, will be updated by webhook
                    TransactionDate = DateTime.UtcNow
                };

                await _donationRepository.CreateTransactionAsync(transaction);

                // If donor provided an email, update the payment intent with it
                if (!string.IsNullOrEmpty(donation.DonorEmail))
                {
                    await _stripeService.UpdatePaymentIntentAsync(paymentIntent.Id, donation.DonorEmail);
                }

                // Return the client secret to the frontend
                return Ok(new PaymentIntentDto
                {
                    ClientSecret = paymentIntent.ClientSecret,
                    PaymentIntentId = paymentIntent.Id
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating donation");
                return StatusCode(500, "An error occurred while processing your donation. Please try again later.");
            }
        }

        // POST: api/Donations/webhook
        [HttpPost("webhook")]
        [AllowAnonymous]
        public async Task<IActionResult> HandleStripeWebhook()
        {
            var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();

            try
            {
                var stripeEvent = _stripeService.ConstructWebhookEvent(
                    json,
                    Request.Headers["Stripe-Signature"]
                );

                // Handle the event based on its type
                if (stripeEvent.Type == Events.PaymentIntentSucceeded)
                {
                    var paymentIntent = stripeEvent.Data.Object as PaymentIntent;
                    await HandleSuccessfulPayment(paymentIntent);
                }
                else if (stripeEvent.Type == Events.PaymentIntentPaymentFailed)
                {
                    var paymentIntent = stripeEvent.Data.Object as PaymentIntent;
                    await HandleFailedPayment(paymentIntent);
                }

                return Ok();
            }
            catch (StripeException ex)
            {
                _logger.LogError(ex, "Error handling Stripe webhook");
                return BadRequest();
            }
        }

        // PUT: api/Donations/5/process
        [HttpPut("{id}/process")]
        [Authorize(Roles = "Admin,ExecutiveAdmin")]
        public async Task<IActionResult> ProcessDonation(int id)
        {
            var donation = await _donationRepository.GetDonationByIdAsync(id);

            if (donation == null)
            {
                return NotFound();
            }

            var username = User.Identity?.Name ?? "Unknown";
            
            donation.ProcessedBy = username;
            donation.ProcessedDate = DateTime.UtcNow;
            
            await _donationRepository.UpdateDonationAsync(donation);
            
            return NoContent();
        }

        // Private helper methods
        private async Task HandleSuccessfulPayment(PaymentIntent paymentIntent)
        {
            try
            {
                // Find the transaction by payment intent ID
                var donations = await _donationRepository.GetAllDonationsAsync();
                var donation = donations.FirstOrDefault(d => 
                    d.Transaction != null && 
                    d.Transaction.PaymentIntentId == paymentIntent.Id);

                if (donation?.Transaction != null)
                {
                    // Get charge details
                    var charge = await _stripeService.GetChargeFromPaymentIntentAsync(paymentIntent.Id);
                    
                    // Update transaction details
                    donation.Transaction.Status = paymentIntent.Status;
                    donation.Transaction.PaymentMethod = charge?.PaymentMethodDetails?.Type ?? "unknown";
                    
                    if (charge?.PaymentMethodDetails?.Card != null)
                    {
                        donation.Transaction.LastFour = charge.PaymentMethodDetails.Card.Last4;
                        donation.Transaction.CardBrand = charge.PaymentMethodDetails.Card.Brand;
                    }
                    
                    donation.Transaction.ReceiptUrl = charge?.ReceiptUrl;
                    
                    await _donationRepository.UpdateTransactionAsync(donation.Transaction);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error handling successful payment for PaymentIntent {paymentIntent.Id}");
            }
        }

        private async Task HandleFailedPayment(PaymentIntent paymentIntent)
        {
            try
            {
                // Find the transaction by payment intent ID
                var donations = await _donationRepository.GetAllDonationsAsync();
                var donation = donations.FirstOrDefault(d => 
                    d.Transaction != null && 
                    d.Transaction.PaymentIntentId == paymentIntent.Id);

                if (donation?.Transaction != null)
                {
                    // Update transaction status
                    donation.Transaction.Status = paymentIntent.Status;
                    donation.Transaction.ErrorMessage = paymentIntent.LastPaymentError?.Message;
                    
                    await _donationRepository.UpdateTransactionAsync(donation.Transaction);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error handling failed payment for PaymentIntent {paymentIntent.Id}");
            }
        }
    }
}
