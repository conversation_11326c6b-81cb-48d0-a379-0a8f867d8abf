using CabUCA.API.Models;
using Microsoft.Extensions.Configuration;
using Stripe;
using System;
using System.Threading.Tasks;

namespace CabUCA.API.Services
{
    public class StripeService
    {
        private readonly IConfiguration _configuration;
        private readonly string _stripeSecretKey;
        private readonly string _stripeWebhookSecret;

        public StripeService(IConfiguration configuration)
        {
            _configuration = configuration;
            _stripeSecretKey = _configuration["Stripe:SecretKey"];
            _stripeWebhookSecret = _configuration["Stripe:WebhookSecret"];
            
            // Configure Stripe API key
            StripeConfiguration.ApiKey = _stripeSecretKey;
        }

        public async Task<PaymentIntent> CreatePaymentIntentAsync(decimal amount, string currency = "aud", string description = null)
        {
            var options = new PaymentIntentCreateOptions
            {
                Amount = (long)(amount * 100), // Convert to cents
                Currency = currency,
                Description = description,
                AutomaticPaymentMethods = new PaymentIntentAutomaticPaymentMethodsOptions
                {
                    Enabled = true,
                },
                ReceiptEmail = null, // Will be set later when we have the donor's email
            };

            var service = new PaymentIntentService();
            return await service.CreateAsync(options);
        }

        public async Task<PaymentIntent> UpdatePaymentIntentAsync(string paymentIntentId, string receiptEmail = null)
        {
            var options = new PaymentIntentUpdateOptions();
            
            if (!string.IsNullOrEmpty(receiptEmail))
            {
                options.ReceiptEmail = receiptEmail;
            }

            var service = new PaymentIntentService();
            return await service.UpdateAsync(paymentIntentId, options);
        }

        public async Task<PaymentIntent> RetrievePaymentIntentAsync(string paymentIntentId)
        {
            var service = new PaymentIntentService();
            return await service.GetAsync(paymentIntentId);
        }

        public Event ConstructWebhookEvent(string json, string signatureHeader)
        {
            return EventUtility.ConstructEvent(json, signatureHeader, _stripeWebhookSecret);
        }

        public async Task<Charge> GetChargeFromPaymentIntentAsync(string paymentIntentId)
        {
            var service = new ChargeService();
            var charges = await service.ListAsync(new ChargeListOptions
            {
                PaymentIntent = paymentIntentId
            });

            return charges.Data.FirstOrDefault();
        }
    }
}
