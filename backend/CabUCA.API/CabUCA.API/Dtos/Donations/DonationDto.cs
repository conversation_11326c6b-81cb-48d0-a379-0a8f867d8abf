using System;

namespace CabUCA.API.Dtos.Donations
{
    public class DonationDto
    {
        public int Id { get; set; }
        public decimal Amount { get; set; }
        public DateTime DonationDate { get; set; }
        public string Purpose { get; set; }
        public string? DonorName { get; set; }
        public string? DonorEmail { get; set; }
        public string? DonorPhone { get; set; }
        public bool IsAnonymous { get; set; }
        public string? Message { get; set; }
        public int? EventId { get; set; }
        public string? EventTitle { get; set; }
        public string? TransactionStatus { get; set; }
        public string? PaymentMethod { get; set; }
        public string? ReceiptUrl { get; set; }
        public string? ProcessedBy { get; set; }
        public DateTime? ProcessedDate { get; set; }
    }
}
