using System;

namespace CabUCA.API.Dtos.Donations
{
    public class TransactionDto
    {
        public int Id { get; set; }
        public int DonationId { get; set; }
        public string PaymentIntentId { get; set; }
        public string Status { get; set; }
        public string PaymentMethod { get; set; }
        public DateTime TransactionDate { get; set; }
        public string? LastFour { get; set; }
        public string? CardBrand { get; set; }
        public string? ErrorMessage { get; set; }
        public string? ReceiptUrl { get; set; }
    }
}
