using System;
using System.ComponentModel.DataAnnotations;

namespace CabUCA.API.Dtos.Donations
{
    public class CreateDonationDto
    {
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public decimal Amount { get; set; }
        
        public string Purpose { get; set; } = "General";
        
        public string? DonorName { get; set; }
        
        [EmailAddress]
        public string? DonorEmail { get; set; }
        
        [Phone]
        public string? DonorPhone { get; set; }
        
        public bool IsAnonymous { get; set; }
        
        public string? Message { get; set; }
        
        public int? EventId { get; set; }
    }
}
