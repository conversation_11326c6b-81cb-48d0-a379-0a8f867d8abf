using CabUCA.API.Dtos.Donations;
using CabUCA.API.Models;
using System;

namespace CabUCA.API.Mappers
{
    public static class DonationMappers
    {
        public static DonationDto ToDto(this Donation donation)
        {
            return new DonationDto
            {
                Id = donation.Id,
                Amount = donation.Amount,
                DonationDate = donation.DonationDate,
                Purpose = donation.Purpose,
                DonorName = donation.DonorName,
                DonorEmail = donation.DonorEmail,
                DonorPhone = donation.DonorPhone,
                IsAnonymous = donation.IsAnonymous,
                Message = donation.Message,
                EventId = donation.EventId,
                EventTitle = donation.Event?.Title,
                TransactionStatus = donation.Transaction?.Status,
                PaymentMethod = donation.Transaction?.PaymentMethod,
                ReceiptUrl = donation.Transaction?.ReceiptUrl,
                ProcessedBy = donation.ProcessedBy,
                ProcessedDate = donation.ProcessedDate
            };
        }

        public static DonationSummaryDto ToSummaryDto(this Donation donation)
        {
            return new DonationSummaryDto
            {
                Id = donation.Id,
                Amount = donation.Amount,
                DonationDate = donation.DonationDate,
                Purpose = donation.Purpose,
                DonorName = donation.IsAnonymous ? "Anonymous" : donation.DonorName ?? "Anonymous",
                IsAnonymous = donation.IsAnonymous,
                TransactionStatus = donation.Transaction?.Status ?? "Pending"
            };
        }

        public static Donation ToEntity(this CreateDonationDto donationDto)
        {
            return new Donation
            {
                Amount = donationDto.Amount,
                DonationDate = DateTime.UtcNow,
                Purpose = donationDto.Purpose,
                DonorName = donationDto.IsAnonymous ? null : donationDto.DonorName,
                DonorEmail = donationDto.IsAnonymous ? null : donationDto.DonorEmail,
                DonorPhone = donationDto.IsAnonymous ? null : donationDto.DonorPhone,
                IsAnonymous = donationDto.IsAnonymous,
                Message = donationDto.Message,
                EventId = donationDto.EventId
            };
        }

        public static TransactionDto ToDto(this Transaction transaction)
        {
            return new TransactionDto
            {
                Id = transaction.Id,
                DonationId = transaction.DonationId,
                PaymentIntentId = transaction.PaymentIntentId,
                Status = transaction.Status,
                PaymentMethod = transaction.PaymentMethod,
                TransactionDate = transaction.TransactionDate,
                LastFour = transaction.LastFour,
                CardBrand = transaction.CardBrand,
                ErrorMessage = transaction.ErrorMessage,
                ReceiptUrl = transaction.ReceiptUrl
            };
        }
    }
}
