using CabUCA.API.Data;
using CabUCA.API.Interfaces;
using CabUCA.API.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CabUCA.API.Repository
{
    public class DonationRepository : IDonationRepository
    {
        private readonly AppDbContext _context;

        public DonationRepository(AppDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Donation>> GetAllDonationsAsync()
        {
            return await _context.Donations
                .Include(d => d.Transaction)
                .Include(d => d.Event)
                .OrderByDescending(d => d.DonationDate)
                .ToListAsync();
        }

        public async Task<Donation> GetDonationByIdAsync(int id)
        {
            return await _context.Donations
                .Include(d => d.Transaction)
                .Include(d => d.Event)
                .FirstOrDefaultAsync(d => d.Id == id);
        }

        public async Task<IEnumerable<Donation>> GetDonationsByEventIdAsync(int eventId)
        {
            return await _context.Donations
                .Include(d => d.Transaction)
                .Where(d => d.EventId == eventId)
                .OrderByDescending(d => d.DonationDate)
                .ToListAsync();
        }

        public async Task<Donation> CreateDonationAsync(Donation donation)
        {
            // Set default values if not provided
            if (donation.DonationDate == default)
                donation.DonationDate = DateTime.UtcNow;
            
            await _context.Donations.AddAsync(donation);
            await _context.SaveChangesAsync();
            return donation;
        }

        public async Task<Transaction> CreateTransactionAsync(Transaction transaction)
        {
            // Set default values if not provided
            if (transaction.TransactionDate == default)
                transaction.TransactionDate = DateTime.UtcNow;
            
            await _context.Transactions.AddAsync(transaction);
            await _context.SaveChangesAsync();
            return transaction;
        }

        public async Task<Transaction> UpdateTransactionAsync(Transaction transaction)
        {
            _context.Transactions.Update(transaction);
            await _context.SaveChangesAsync();
            return transaction;
        }

        public async Task<Donation> UpdateDonationAsync(Donation donation)
        {
            _context.Donations.Update(donation);
            await _context.SaveChangesAsync();
            return donation;
        }

        public async Task<bool> DonationExistsAsync(int id)
        {
            return await _context.Donations.AnyAsync(d => d.Id == id);
        }

        public async Task<IEnumerable<Donation>> GetDonationsByPurposeAsync(string purpose)
        {
            return await _context.Donations
                .Include(d => d.Transaction)
                .Where(d => d.Purpose == purpose)
                .OrderByDescending(d => d.DonationDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Donation>> GetDonationsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Donations
                .Include(d => d.Transaction)
                .Where(d => d.DonationDate >= startDate && d.DonationDate <= endDate)
                .OrderByDescending(d => d.DonationDate)
                .ToListAsync();
        }
    }
}
