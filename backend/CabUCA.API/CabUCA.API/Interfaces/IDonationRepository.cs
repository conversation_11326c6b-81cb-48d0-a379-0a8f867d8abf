using CabUCA.API.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CabUCA.API.Interfaces
{
    public interface IDonationRepository
    {
        Task<IEnumerable<Donation>> GetAllDonationsAsync();
        Task<Donation> GetDonationByIdAsync(int id);
        Task<IEnumerable<Donation>> GetDonationsByEventIdAsync(int eventId);
        Task<Donation> CreateDonationAsync(Donation donation);
        Task<Transaction> CreateTransactionAsync(Transaction transaction);
        Task<Transaction> UpdateTransactionAsync(Transaction transaction);
        Task<Donation> UpdateDonationAsync(Donation donation);
        Task<bool> DonationExistsAsync(int id);
        Task<IEnumerable<Donation>> GetDonationsByPurposeAsync(string purpose);
        Task<IEnumerable<Donation>> GetDonationsByDateRangeAsync(DateTime startDate, DateTime endDate);
    }
}
